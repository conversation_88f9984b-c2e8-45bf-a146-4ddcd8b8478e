{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98708b7f478320ba0258ec9e94a0c35c03", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_osm_plugin", "PRODUCT_NAME": "flutter_osm_plugin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980a114b328fadf3e71f9d7ad93f1e0e58", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98558247d3c9e61bee0141ee29e5b6b29f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_osm_plugin", "PRODUCT_NAME": "flutter_osm_plugin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98283189b3afbc4fcd51922087d47274c1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98558247d3c9e61bee0141ee29e5b6b29f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_osm_plugin/flutter_osm_plugin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_osm_plugin", "PRODUCT_NAME": "flutter_osm_plugin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9889c498a49ebe9c38ca56fcd35dd93eec", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989542105a3ea5d05b88c6ad26b24b72d5", "guid": "bfdfe7dc352907fc980b868725387e989c6edb273e117d13114e9f1213e2ac16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888a4bf3e8beda28b74e3976d4006b515", "guid": "bfdfe7dc352907fc980b868725387e98ddbc8a499240c52150634efb9441b47f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989922041e439cc665470238e941a65598", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dc5067fdf56db157802036086f5e0f36", "guid": "bfdfe7dc352907fc980b868725387e9850c650010539459184190f912fbb6dda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f856d35767a51564961b7343543ecab", "guid": "bfdfe7dc352907fc980b868725387e98148c6fbfb54c8f57291a909dd7e08e29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3acdbe694e2683da33b6c147aa29292", "guid": "bfdfe7dc352907fc980b868725387e98aa822c9dc0c9ecbdece6a2422b2659d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981616333fdbd86f35f2126900f1a04649", "guid": "bfdfe7dc352907fc980b868725387e987a56291db11a3eff2578e86a385354dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c60b5829f3fef699b92963c45eca8111", "guid": "bfdfe7dc352907fc980b868725387e98a68d955eb6d54017ed9ae352143e919c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e549cf503fa3d9ff956ab4bdf15a5b9", "guid": "bfdfe7dc352907fc980b868725387e980d19143efabcd07b3b34dae3c445cb52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849005745b0931f5383ae79d347e28280", "guid": "bfdfe7dc352907fc980b868725387e9808d268fdb79e7e33b26353d56b4cc1d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880d731e67cde4499c0b5a28e353f37ad", "guid": "bfdfe7dc352907fc980b868725387e98465aa9d56b44cbb5319d53ba5472c2c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e39e3af2858bb8ab328745a295b80a1", "guid": "bfdfe7dc352907fc980b868725387e98283168e8fabf1585e864104b64b56e22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d6dfa2ab69a3785c23f1087307ad40b", "guid": "bfdfe7dc352907fc980b868725387e98a9194a09ee38e29c716a0fcea8d96156"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0b49efff2b72237afc4135e2f31d641", "guid": "bfdfe7dc352907fc980b868725387e986f09ae9ae00dfd30acac8116783c69d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818c0740f614d01ca21f49a18d20b6c7", "guid": "bfdfe7dc352907fc980b868725387e984b86c395a335708844bc2e3fac980fb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeb3671168e2f62c0ded735b8eae5116", "guid": "bfdfe7dc352907fc980b868725387e98ba5d0408da3f33adeef78786e323443c"}], "guid": "bfdfe7dc352907fc980b868725387e9830090b3fb9727f00cc0013a0f7344d7e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e98a704d0467b05bfa732899ce5309b8829"}], "guid": "bfdfe7dc352907fc980b868725387e982b9246c0db286cb8dd905319f49d8d29", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c9feb22313725b181fbbda8de329827", "guid": "bfdfe7dc352907fc980b868725387e988f0faeea981bf3a7f6c8aa1618430b64"}], "guid": "bfdfe7dc352907fc980b868725387e9830000545cd5495d54523bb6dec92a508", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ba3679b4c4428e6a6d83c5308d3af99b", "name": "Alamofire"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98deea8f469e33b13ceb7aa3e97810c0d3", "name": "OSMFlutterFramework"}, {"guid": "bfdfe7dc352907fc980b868725387e9873d2f7f3c0ba6d1fb2b8b6f08bbbf48f", "name": "Polyline"}, {"guid": "bfdfe7dc352907fc980b868725387e985906e783cd19a852cc9239174f1ec1db", "name": "Yams"}], "guid": "bfdfe7dc352907fc980b868725387e98103762ef0740dc5525f04361cd4e3800", "name": "flutter_osm_plugin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ee27ca28813d9c560284de302d27e0cd", "name": "flutter_osm_plugin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
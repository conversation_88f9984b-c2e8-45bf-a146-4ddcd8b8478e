{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4737d83053e9290cdb2a77d5c915fca", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988b3b87d8600460026ef41751f8dcdab5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984dae33788bf713be87bfe0a91a4e4274", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9897db70be6a51e732456f0e62d20f010b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984dae33788bf713be87bfe0a91a4e4274", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f4db466afbee1f2b83eb0b946abda92b", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98aee8f1acd615dde9e7462d3eae35a9ee", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9831378ac0cd8c3ecd44ea625d1516be39", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d4ade5e43d80d88ad25961e370977531", "guid": "bfdfe7dc352907fc980b868725387e98712f2d35f7160d2684884f3582ae3471"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873907a4a45898846be5df9198785ef1d", "guid": "bfdfe7dc352907fc980b868725387e98379fea6af63efa5045a0fea87be71407"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6b399e5230f6506a23f74b371e86411", "guid": "bfdfe7dc352907fc980b868725387e9835ca8baf9254330e172715f3ebbba655"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837306ec9c2265a14ad5b19996d1827a9", "guid": "bfdfe7dc352907fc980b868725387e987bf2d66cc706afad0920b2c0f90425f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f15872cc245fe6ef91badb2bf75a3bf", "guid": "bfdfe7dc352907fc980b868725387e9851b4906d1e4a9e77384cc20161d40a74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfc5fa4442b38cb4aa778a019e1f6f26", "guid": "bfdfe7dc352907fc980b868725387e982274ecee7f767a96ec88e93b065fd71c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851d3b0ec830beb370e3fdb678b34ebd3", "guid": "bfdfe7dc352907fc980b868725387e98254eb7d2292adbe900722e0177221322"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75e3d1fd8ee5242f76865e4e816b6f3", "guid": "bfdfe7dc352907fc980b868725387e987d778092f5567fc403e93d5f6a732300"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e11dbd4fc70832864d6c7abf0278ac87", "guid": "bfdfe7dc352907fc980b868725387e98019974897b5bed996a9891e650985198"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98746e79c2c0795fadabf414fd5b5ac870", "guid": "bfdfe7dc352907fc980b868725387e98280e51ac51c6c530bb471e57888ad080"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a7221463fcac28a0c17766acca7a407", "guid": "bfdfe7dc352907fc980b868725387e988c51f0bc0fabeac4b2e51bb8b1c4d263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c005830baac32b965677a0abb1b292", "guid": "bfdfe7dc352907fc980b868725387e985862c23be5fc253d73059f3bc84ed189"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d96c5400647e80f3656b35e9284cbb5", "guid": "bfdfe7dc352907fc980b868725387e9867e373f767390242d9f617847d4c0899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d879e505c1d98066b44c0316cb00bc98", "guid": "bfdfe7dc352907fc980b868725387e9860b5d5dece1c30b0a42c05cf553173bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e54b88ff0fff67f7081d326a939bad", "guid": "bfdfe7dc352907fc980b868725387e981839c4bd97f915f34654cc38fcda7a9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803eac8e20c558f8bc458243e0b562d37", "guid": "bfdfe7dc352907fc980b868725387e98b0769bd662063fd651ecc42954f1499c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f281b3fd0e94a9f3aff4bb965ce8020", "guid": "bfdfe7dc352907fc980b868725387e983f674cb50a133b24ebad4d1f92ddeef6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835d7041541e7dde9af36aece1870b2ed", "guid": "bfdfe7dc352907fc980b868725387e9849e8baede9cc13bc68bb25b95ce4d92a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daea1d7b2bbf594743b28abe3aae489d", "guid": "bfdfe7dc352907fc980b868725387e98f58b13a83de6e9103e54c7a3a75a5848"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d078f0739153ac63868488a2a4fa17", "guid": "bfdfe7dc352907fc980b868725387e98871d79e494622190d55a9d4080d37ec8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92932c6f97f0452c2efe03f54ebcc38", "guid": "bfdfe7dc352907fc980b868725387e98e55aa546045c20e0b045767a255d447b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ab4f38864957e52868ace55ace6d1f", "guid": "bfdfe7dc352907fc980b868725387e98746e354377c89e5de1c63d3c2e129a21"}], "guid": "bfdfe7dc352907fc980b868725387e9888750f1dfc288ac6b926e7c5f5e598eb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
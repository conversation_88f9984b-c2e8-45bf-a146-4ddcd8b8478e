{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c4d91f382af7d3b1568c11ac51887224", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b9c8d9512a3e3040293e77aea5e2fea0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b9c8d9512a3e3040293e77aea5e2fea0", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98779441ba5bf19286d14df8331e04d15b", "guid": "bfdfe7dc352907fc980b868725387e98451271ac92a2a6b3bc220a26c9165552", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b433f715011b636de11f82865b924c5", "guid": "bfdfe7dc352907fc980b868725387e98803fd33d35f2b4a487541d8ec4e94db2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7504952cab96cd6b07e8f085281bb53", "guid": "bfdfe7dc352907fc980b868725387e98ca13dab421a323308e8ddc6cdb237977", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3e5ea8ec3b4e7496f22e0674106ba33", "guid": "bfdfe7dc352907fc980b868725387e9855863e1daf52a474496c62b8e26b1623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b135b7b4375edeeb83183f2ca34f9a3", "guid": "bfdfe7dc352907fc980b868725387e98fbeb2ccbdccf43c8c39119e52627393e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982058500c8e8ed1bf66b5257cc58c5d5a", "guid": "bfdfe7dc352907fc980b868725387e98de1977ee82e83b08cdd262f30ab1e546", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839b894232facb666d5cd14ad71ba91ce", "guid": "bfdfe7dc352907fc980b868725387e98f2ff79ac1b761f0f79e693ae36952994", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ab29e2b634aa00224f9d17e08270baa", "guid": "bfdfe7dc352907fc980b868725387e98f1833bff7e312b1000761b520b229d5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98386595a9466aea14ee5f746748e9e1e7", "guid": "bfdfe7dc352907fc980b868725387e98b7a704666dd231f45c8c0c5039cf962c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2a7c9d5ee942a6c56fe5f9c7ea9d54e", "guid": "bfdfe7dc352907fc980b868725387e98d254ab7cac101db2e9608be9268e33df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871075e91874d4194b72bcd2dc472d3e5", "guid": "bfdfe7dc352907fc980b868725387e9841995aad82cee4f0d1af9a0970f1d9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c229dba120867019575901cff2550d", "guid": "bfdfe7dc352907fc980b868725387e98f6a6e186019a7b6bbcf7edf80d739ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802e48e2a0346c22afce9453444c82239", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f28cbe49f31678fc7137e82bf6f756d3", "guid": "bfdfe7dc352907fc980b868725387e9878bf6ea35872ff69d8716d14e8726fb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def5367235544c13d2e12e281f755be3", "guid": "bfdfe7dc352907fc980b868725387e980929dec59f51f260fb14375cd3aee123", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c1048f465d9eef4a134969358eb8bb9", "guid": "bfdfe7dc352907fc980b868725387e98978956dcfaf4dcaa425b470b0ab6591c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3764861a443fa644bd2a33b2be822cd", "guid": "bfdfe7dc352907fc980b868725387e98d3e49fdf20728c8da29875785f93fa34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b357fd26f3c25f4eb638411556c6a98", "guid": "bfdfe7dc352907fc980b868725387e9875ed083cdffa950109d382aa9bb635a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fbf0dd7ab26b9c74452affcc0ad9f85", "guid": "bfdfe7dc352907fc980b868725387e98da892b09f0181aca06faf2f98c1b6abd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4f2d27b932392f21d252c0723d712a", "guid": "bfdfe7dc352907fc980b868725387e98e03749eb0dfd6a1bd972248fd565e7f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c51fb421bc6ef3272fe0abf7047aefa", "guid": "bfdfe7dc352907fc980b868725387e98ba32cbf0b2f954399f58199c72f84410", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dd80ddcac9cf1ec81012694c9fdec19", "guid": "bfdfe7dc352907fc980b868725387e981ec4f2ee3baa2378c72056f6d51532bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811dc86efdc284f816ebd86dad61f6efb", "guid": "bfdfe7dc352907fc980b868725387e981f1bb3e1716086026593817395ba776c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2cb3e57942685f5702f59faf66d502e", "guid": "bfdfe7dc352907fc980b868725387e98c78b17d465f66fc46ff613f796c4b8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec98992b723cf5be3cb90d7781eb31e9", "guid": "bfdfe7dc352907fc980b868725387e98e2fdcfc26cf4ff69bfe0979351b649db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d70747e7abbc72083310a5f14a77b36", "guid": "bfdfe7dc352907fc980b868725387e98d5c7402eee4379664783cc3196df872b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877caa532be5d198fb1d09c244866730e", "guid": "bfdfe7dc352907fc980b868725387e98f2b049120e51de6572d77eb4fa27601b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a576dadd052cfe9a109d5acffec45c7", "guid": "bfdfe7dc352907fc980b868725387e98470c0a98e19494a4f7a55af39256cbd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ab65da7545ca5e051d22353482efe8", "guid": "bfdfe7dc352907fc980b868725387e985e260f4f5b1601c288ba7f6d58a3836b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d9a722e148eedc94cbb2713273cf5be", "guid": "bfdfe7dc352907fc980b868725387e980a606703b1dc9d426d651976283c7afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98befabba5208c7149aa49de74765b50c1", "guid": "bfdfe7dc352907fc980b868725387e98794fa1083299c389c3e06b1da4679eb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829f56e614c4c7555c4ed3b23718b6ab5", "guid": "bfdfe7dc352907fc980b868725387e984f81bf588b7132779cfda37357f5988a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e64b3289243c12b04a94839b8730037", "guid": "bfdfe7dc352907fc980b868725387e983b380180ac8372c476a807b9287534c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f64ad89f2a02c1581bd0b004191e00fb", "guid": "bfdfe7dc352907fc980b868725387e9826dbc66437fe08e5f660cd7d57a07ade"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981aa831582dce3930e949780de9d3a912", "guid": "bfdfe7dc352907fc980b868725387e9848228779c89b7cf4c56f96d9771d2ce3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b35971d4a7d2749c590d814f15b5ae7f", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ee1fad4e706808b4d4523ea22ae8edb", "guid": "bfdfe7dc352907fc980b868725387e98ff0bc9241cfa1703ee578d05d882114d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869fe7d1c174377a307a16dd2a8ad8341", "guid": "bfdfe7dc352907fc980b868725387e98dc21c349147484be8818b0c698c008d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b18a19993f0e595a39759e6cb659f4f6", "guid": "bfdfe7dc352907fc980b868725387e9854ae6e6291c1d1636792ade71a7bed6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d20493b8151b26c4253054e77d38dc", "guid": "bfdfe7dc352907fc980b868725387e98a76ab6623a0005b7a05de9a09b4bbe0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ec42406601d8d2afa8499233f8c9519", "guid": "bfdfe7dc352907fc980b868725387e985233b06bb9e39497179a34ca3d93e52b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98403fee73e3452d1ecfc35c36354f7f88", "guid": "bfdfe7dc352907fc980b868725387e98bdf453b73e17a86d84100cf68d60e04b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98982cad3e0360956aed2b05e52fbf64a8", "guid": "bfdfe7dc352907fc980b868725387e98700a094c3bb1e173435375a335e28e84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cafec8257a6c5c0ed99bbe17fb9e6765", "guid": "bfdfe7dc352907fc980b868725387e981d3f412c09d65d91f50cc28e7385ca9f"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
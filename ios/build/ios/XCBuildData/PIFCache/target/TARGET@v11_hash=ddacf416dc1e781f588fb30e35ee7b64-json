{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9832e40255d8f34c0fcfc95dbda689de2c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98121b0755a8b62b0b8ed71a6e8b8f6672", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7b6d08f344058faacc72b8ec77fc59f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981c6a68d54485c4154207d181c238992a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f7b6d08f344058faacc72b8ec77fc59f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Alamofire/Alamofire-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Alamofire/Alamofire-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Alamofire/Alamofire.modulemap", "PRODUCT_MODULE_NAME": "Alamofire", "PRODUCT_NAME": "Alamofire", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98482ad45bb9aadff03128cfb2d6392b79", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896a6b0551cdcb8840674407fb1a0f154", "guid": "bfdfe7dc352907fc980b868725387e98eabf857b26ef976742b01ee3c376981f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e982714de58869b100548f19e60899359f1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a0e74bb73724268ef67bc7eceab947b6", "guid": "bfdfe7dc352907fc980b868725387e985b686ed4d6dac509af5b9b94214a96c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989072b56eda70da31f8ffa29c5e2695ab", "guid": "bfdfe7dc352907fc980b868725387e9801f26390bfdb9c87ac541e1d55423a0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e802133f0e24c20c3f57712f834f6a94", "guid": "bfdfe7dc352907fc980b868725387e983d28b9f0db0234b5e5700a485e273710"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d231657824b706356b8de98b01133c54", "guid": "bfdfe7dc352907fc980b868725387e983350a3f0f85300fe64b83e7c4a0878cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864e360e415519a30a399b79590fb281b", "guid": "bfdfe7dc352907fc980b868725387e98941ec65b65566af2b941c9816e9f0a6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efe32b2ca90f50ee63c77969872c40d2", "guid": "bfdfe7dc352907fc980b868725387e98a1b2c8266eb21831ea28e0c4e67cf9a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b6aabac013f7e5ce6ebd104e06be45b", "guid": "bfdfe7dc352907fc980b868725387e9854370bcdde1de474af1824e6edcc877f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860bee7bf57782d8e5b8cf6c11c3c590a", "guid": "bfdfe7dc352907fc980b868725387e9810bf31f402db2e307a62693acd3fff11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e718e2ea79e9c7d21c2c98de2bf85856", "guid": "bfdfe7dc352907fc980b868725387e984cb35c76a0b102f6cbfd03415421f2cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e6117609435fb9bbe56fac4e1284614", "guid": "bfdfe7dc352907fc980b868725387e98c0eae51003d7ce609068908811952f58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c37e9e33e1063e92f13a69636f32fa2", "guid": "bfdfe7dc352907fc980b868725387e98f5a8cf3fce24ca5890f537a6bf478285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db926d216f396278551b624a0b8db6fd", "guid": "bfdfe7dc352907fc980b868725387e98969a05a163becc67e67559abeba1609d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98322ccfddd185fb5cb975597650d3a5d0", "guid": "bfdfe7dc352907fc980b868725387e98a940a52b6c0a1ad081bf34d8906cb5cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b06359f1d351956b1d182c908e8a3b", "guid": "bfdfe7dc352907fc980b868725387e98312294a959af53815804f6b3a2ec9883"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f43ed6b418156d9158c6e48b7936538", "guid": "bfdfe7dc352907fc980b868725387e98b21f1a4a0262108a71848bb6abe105bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800e82a724149d41cbc4a0fd5eafcb2da", "guid": "bfdfe7dc352907fc980b868725387e98bd465a96267ec176021587b8c203c8c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f19f5e8e43290891cd6c3e3d8a6d98de", "guid": "bfdfe7dc352907fc980b868725387e987fa28c047f5ba9909e474571038f4ec9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dfd89f94623bbef7731ef71daf01385", "guid": "bfdfe7dc352907fc980b868725387e98653be64ec168091a3ac8937767467cdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bdc1907eb0e575b50def53f17190d4b", "guid": "bfdfe7dc352907fc980b868725387e98e84a9a2d9e9fafc59517ae0c35d89337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987293f800b2e055b7dec940377250d6bc", "guid": "bfdfe7dc352907fc980b868725387e987c70b9566b553fea2f0f8d462915120b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878ecea89b71c4dc5302f1bdea0757b29", "guid": "bfdfe7dc352907fc980b868725387e98069d62851ac1903c5642745f9716be81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf38baa0955f1348dfb63e595503bc4b", "guid": "bfdfe7dc352907fc980b868725387e987283aa78c0203ea06bc90dd657f15b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd3eaf8b1c0766dff853c9a789c1b2f9", "guid": "bfdfe7dc352907fc980b868725387e98e7dfe2777851053df0dbf2479dcd7b43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0b1b1c61cabf84f3a6222c7fd501de3", "guid": "bfdfe7dc352907fc980b868725387e98481a9a0d780377efa6643e6d0229b180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d96485b5483a9ee845f720c2d3d0ff77", "guid": "bfdfe7dc352907fc980b868725387e988cf474d1416f27b2ab76d9c5b3a10890"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98812622b2213144f8d6607dd78b5060c6", "guid": "bfdfe7dc352907fc980b868725387e98d909467045a5ef04dc3d31660ecb880b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bc90e7767596c67b4571074b7d9efd6", "guid": "bfdfe7dc352907fc980b868725387e983bc577800c5c2611e27b456e3aee93c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857e9297858372c1596038baa508f3741", "guid": "bfdfe7dc352907fc980b868725387e989881738c86a0e2dccbaad476742c01bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8eaba6025c5478d6c794f64919a9cbd", "guid": "bfdfe7dc352907fc980b868725387e98391529c8998c51bfd0606eeaca1a6a46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b575558bccad3e167d18090b5715a3e3", "guid": "bfdfe7dc352907fc980b868725387e9868163c93bc26b33b80f86d32b0c3ec1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98524cd9a20d73f14c032ae1b8244f5aeb", "guid": "bfdfe7dc352907fc980b868725387e98f5bf4df0961ca66dbfd21b1af236c0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98432f0253da051af02f344ae20b042cf1", "guid": "bfdfe7dc352907fc980b868725387e980e7fab2bcbe3387d38d63313656d1a69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8629eb9eb6908f5770b6f86f63877a4", "guid": "bfdfe7dc352907fc980b868725387e98590d8e35aa1fd2b1bbefecb8b9cc2ef5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98278727955bdd8c3d545cfc6bc201259e", "guid": "bfdfe7dc352907fc980b868725387e98d3a6767af2e0439af8de06ce7c82795c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b497c8e79039c19c08a29597d47e0ffa", "guid": "bfdfe7dc352907fc980b868725387e9818e980cafd758392e5a0f2798d510740"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff673e70b01c952a811f4392155c9df4", "guid": "bfdfe7dc352907fc980b868725387e98108fdf4e9ee307e4ad6605c8feb30cc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e918c4ef0a18a1083c7562fd72e87e4f", "guid": "bfdfe7dc352907fc980b868725387e9804a4a476ecf801ffbc03ae8d680d5847"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a301c430afcd901afb0d50c0a879f3f", "guid": "bfdfe7dc352907fc980b868725387e98288bdd8aba74c16339dfd13bb05474bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c1f8e46cb1c4a48cc140ad02625600f", "guid": "bfdfe7dc352907fc980b868725387e9883fc5317a05af86fd8924cf1bfc2a682"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a2424a900a4e4e3b47bf021eaff22b2", "guid": "bfdfe7dc352907fc980b868725387e983a03a99a0f7588db0594eab2fe66191d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805d49b518ef07904db48923ff8c2b92c", "guid": "bfdfe7dc352907fc980b868725387e98829d47dbe33611e157c204cd4f362ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a116933f01d2de094fb7295afeace21d", "guid": "bfdfe7dc352907fc980b868725387e98b27640b9bb5b87601cfd38838681e8e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c626079b6bf2f07e0ee712e162610d2b", "guid": "bfdfe7dc352907fc980b868725387e982abde40c6fa8606ad0c823b6cecb8105"}], "guid": "bfdfe7dc352907fc980b868725387e986738216bf3432740b8b6261eb86e6a7f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824d585ba56ff05a9a1936345b9ba27ad", "guid": "bfdfe7dc352907fc980b868725387e982f12b8fd0303269b0a973eebc22ae634"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e98b64abcfa4502efbeed9941b9f08dbc31"}], "guid": "bfdfe7dc352907fc980b868725387e9828ef15012b09ad33035a1c4da4bd8f72", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc76c8907ab85411f2ad32e447c1fb58", "targetReference": "bfdfe7dc352907fc980b868725387e98c0cf7d8ee0c03c9d9476c5a72bca59ee"}], "guid": "bfdfe7dc352907fc980b868725387e983ab21375c7ea070327155cd87e1cf0fa", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c0cf7d8ee0c03c9d9476c5a72bca59ee", "name": "Alamofire-Alamofire"}], "guid": "bfdfe7dc352907fc980b868725387e98ba3679b4c4428e6a6d83c5308d3af99b", "name": "Alamofire", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9878a7912f930d33f69a5abbeb079e5b03", "name": "Alamofire.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9874d7ff90bad8331e3140610ad244fe10", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d572be75eeda9d44979baba16e69cb3d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984cc92ce6bffd6a04d19242854412b8b4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984d5aac9d728d6301eec8ca57b1adcdb4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984cc92ce6bffd6a04d19242854412b8b4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983045251e9ccbc8c878e2c858503518ee", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b6feca7da4bff6af2283ad1cc9eb733a", "guid": "bfdfe7dc352907fc980b868725387e98a0244b1d744b48c6bfe1c180eecba3ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd87fe59d07f84d291a70adf586d100", "guid": "bfdfe7dc352907fc980b868725387e98d9c4b2efe378b8e71fc789e2d7e8734f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3db3be99b479252d3454da9b822281d", "guid": "bfdfe7dc352907fc980b868725387e98aeb23f6d84359a289c776ce00a1f9cbe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98972015fcef6560f7ca8105a0b772cd47", "guid": "bfdfe7dc352907fc980b868725387e980ee8f60f0868bd7695db3292dbf5c2c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857e2508e7864ba2fae0d71003b7e0079", "guid": "bfdfe7dc352907fc980b868725387e9808380f1e66a62e9a55c97f54dd46091f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857fac22eaa08c9ada042f9cf9cb44341", "guid": "bfdfe7dc352907fc980b868725387e98b8db9a874319ec43d1c0ee81f2ae86b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819ad12d83adb389937e2ab546c02171f", "guid": "bfdfe7dc352907fc980b868725387e98567d3785872dff1a2b8a51fe0745690d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98456952f99014d948054ac469ad6f4bb8", "guid": "bfdfe7dc352907fc980b868725387e98892c49eed6fd08991f72d8c632afce9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988839996ed61fce9403b81b7cceeee744", "guid": "bfdfe7dc352907fc980b868725387e985e7a37765236f1e076dc7a83c32fd888", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffd90e8e11796a1ff71918f5de3da358", "guid": "bfdfe7dc352907fc980b868725387e98539f2fbedfef5a79cfeb1784aaa56859", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8269154f8fe9e5b1b9648ebd4068460", "guid": "bfdfe7dc352907fc980b868725387e9844b48dc5ae3fd6d6319f4dcce0942640", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab284959465ded48b5a3f601bc4cbc48", "guid": "bfdfe7dc352907fc980b868725387e98f57d23314e75e85bd9350d176e0fae6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982556dc9081a91c35aa97c88ea7ecf72a", "guid": "bfdfe7dc352907fc980b868725387e986a04fe0d5135f688f19e2214331beb50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b1c77b624a6d9657b4a014b64611c1", "guid": "bfdfe7dc352907fc980b868725387e989dc0a81ec2f8eb8761b68663b8689686", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988473507cda8c92e93573c6b1096a14bb", "guid": "bfdfe7dc352907fc980b868725387e9857d5dd879286337bc823a53ab6ad5fe3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c80bd9208c8e0d871793804af261904", "guid": "bfdfe7dc352907fc980b868725387e9887776930f4949d8177694fdb2d7518da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aad36443ac3d05e6ed0f33a09e7f8f0", "guid": "bfdfe7dc352907fc980b868725387e98a3cb115998d2dd28258e037e734868d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aed84dfdfa7fca575db6bc4f54a06ff5", "guid": "bfdfe7dc352907fc980b868725387e987ebe307e487d36d609a4ee41841b86fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346b491753c3cd4d313e6caf13977115", "guid": "bfdfe7dc352907fc980b868725387e985089f5a0a5992b09409ce4d3cb48ac1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb8f331bceec9608006211611b32282d", "guid": "bfdfe7dc352907fc980b868725387e98c8131447d265cdf25f8b6895a077ceb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98569b67afb7cc06c532040b89ceeb4ea3", "guid": "bfdfe7dc352907fc980b868725387e98207dc334240f9b34937128d8a39a7357", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de0153e01c1e90da0430c6364fd60933", "guid": "bfdfe7dc352907fc980b868725387e98275c4a00b09d12aab67fe969227d0923", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887f4a6f80187e31ee96f3e44650cd6ae", "guid": "bfdfe7dc352907fc980b868725387e98a55f76314ff75b442ab402c5082e3edd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e0da05a9cb8dfee13579ec91e9fc1a1", "guid": "bfdfe7dc352907fc980b868725387e98e6ac7f03c3c56cfd160450f6d7576be9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985556692480db762dd32e1f2d264188e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cd2c1ff604a7c32612d685cc9d65ac02", "guid": "bfdfe7dc352907fc980b868725387e983dd79d2a03eb927543d8ce6c86616e74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8d86049e10b381ba013a8de6c1674a4", "guid": "bfdfe7dc352907fc980b868725387e9870422c7dda357dd0cc7e6d9ab0d8b759"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0aa3c77536a13b52947b813a382e4ce", "guid": "bfdfe7dc352907fc980b868725387e9864766a0f4ef988d5bc75743eb1562167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884511a596b45703b2515effaee02ad92", "guid": "bfdfe7dc352907fc980b868725387e98b8876716dfe390b93c7eb45baeead83d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819357954677b4e32d7e0733f27d69e8e", "guid": "bfdfe7dc352907fc980b868725387e985147628c7ed18b57e4c1cac8774bbc9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e286d8d77aba0e8c26914296baa81654", "guid": "bfdfe7dc352907fc980b868725387e9826ccef293342da31d75b94eb457d8229"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987142187d4c1c7395b00bf4c7c0461665", "guid": "bfdfe7dc352907fc980b868725387e98ab539dfed616e29f50e837df3a448636"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810d1b84e8dbc8ad5fce33a1ef7997606", "guid": "bfdfe7dc352907fc980b868725387e98ea606719dc65c3da0101965ac688edfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862f12a043c269a4cdee3bcd3ae02f7b2", "guid": "bfdfe7dc352907fc980b868725387e98e587ff4c4532aa9d0565e976457f9bc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807eeba0aa4f460bf1d2dd928b788cf3a", "guid": "bfdfe7dc352907fc980b868725387e98bedaa8e67e735766ff08842b99896018"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988478ff2a8703184f4d6d20650bfaddf1", "guid": "bfdfe7dc352907fc980b868725387e98c8a6938c87480cd4e38cba15311d41e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98348b70afb095a5e95d59efd15af0ef80", "guid": "bfdfe7dc352907fc980b868725387e987e99fa39c704a2915d14e2d6488f49f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdf0c6017d95c37d9ea198d2be74fc99", "guid": "bfdfe7dc352907fc980b868725387e98b7e32972742dcad2981a1670a405c068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980319f00affb4322cb0b697b656fcc688", "guid": "bfdfe7dc352907fc980b868725387e98d8dde0e264ddaea668f2575c04e093fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aada0b0c0f34300e7276339b6a34e03", "guid": "bfdfe7dc352907fc980b868725387e9890c9c9bf494761b26b2de2f983bd183a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b575d93058b27306867913fc47981f89", "guid": "bfdfe7dc352907fc980b868725387e98358d2683c27dabccc005d3f0e9a1ab0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d70f9a09ddaf8d868066964b98761adc", "guid": "bfdfe7dc352907fc980b868725387e98a6475d9843fd6c21f7b9ddf31f2816ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec5fd6994665ae89b30a46fb59a1eff4", "guid": "bfdfe7dc352907fc980b868725387e980f3068da32936c68d61e3d769898e784"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866240d5cd9ec89f6e3fe434d524cf9b1", "guid": "bfdfe7dc352907fc980b868725387e9823a74f47fd3623c8c782504c9638cefe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f5b20fffc7446f9aa5ebf4ec03b04a", "guid": "bfdfe7dc352907fc980b868725387e98b80106290dabb10fdd36448f209391cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aeedf3052562432ed753c8870488ce3", "guid": "bfdfe7dc352907fc980b868725387e98ee6e5bd0ec1e1783c80be2b696c4e454"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22f6ccd2f4d2f01468e72ed03174f4d", "guid": "bfdfe7dc352907fc980b868725387e98cd24330f147a9d2e3dd74d06040a997d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eab030e3a1bed1190232645e7e30e23", "guid": "bfdfe7dc352907fc980b868725387e98adb5b791f20f3bce821e0e7991d80290"}], "guid": "bfdfe7dc352907fc980b868725387e9880a62c7c27ab98f3623503b18b26dc3c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e985c048dd2a28b87147600ba1af421138e"}], "guid": "bfdfe7dc352907fc980b868725387e985c5cb7eab3753c1e8aa8e8638c6063f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8ce9464947c02b3384ac8f1a4209a3a", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e986bbd0814cc9b84f02fc3f4ed2d2f3a21", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
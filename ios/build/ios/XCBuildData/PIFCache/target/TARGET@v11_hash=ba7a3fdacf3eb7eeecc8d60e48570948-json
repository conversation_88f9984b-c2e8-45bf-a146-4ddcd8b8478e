{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800f063279572cee20f6f65713bdac3fc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fc9bf04e8ac578ce181933ec592bc8a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985614711376e60aa59d97089a1beb0114", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c98c86fff114e6efada9cb43e93ab64", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985614711376e60aa59d97089a1beb0114", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810a621892696e4715518a64fd308fbe1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f15f211f90c40cefe191eb398ec646c0", "guid": "bfdfe7dc352907fc980b868725387e9848f2c86709d2311f205f49c727b0d39e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf0e7b3ebed12d4ea9a9beb1591c5e84", "guid": "bfdfe7dc352907fc980b868725387e98908a5db620e5051eba6ac1eb387f2004", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c3b0d7920e35de86897dd752e03fcc6", "guid": "bfdfe7dc352907fc980b868725387e982456a0e41f9bea73123090ffd3bac243", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98002c6aa7213b198b7aff128dc303e158", "guid": "bfdfe7dc352907fc980b868725387e98f5a391bd6080ea8683c2b81b6f3c64e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8e4349690672e4c5d6db1f1fd06ec2", "guid": "bfdfe7dc352907fc980b868725387e98d0b1b00c08626777438a43be98e3ea11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b01a9f4a1945c31888437bb7f3972918", "guid": "bfdfe7dc352907fc980b868725387e98ad1a5ee2e1a0f228a78f2fbea696fc2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143183c5b0cba92e839dbf39d1133d20", "guid": "bfdfe7dc352907fc980b868725387e98d21ee20ec5b17cf609f4a326d11e3afb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea341f756b663df7f5b476c2d2f23673", "guid": "bfdfe7dc352907fc980b868725387e98436bd0fa5fc431d47288ada295eb36dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f224d8d6d82e4718a5f92b18e03ca386", "guid": "bfdfe7dc352907fc980b868725387e9861849a2692c9f35cc36b8d5e71d17f9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859115f12802ef73da9e75320aed3971e", "guid": "bfdfe7dc352907fc980b868725387e983d3aa624ab60c1077e0c7c2bba51d53c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98157ad08cbbceb228fb5fab9897bd3d77", "guid": "bfdfe7dc352907fc980b868725387e98ca867af2ebf73aaafc4b2fcd90dfcbd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855432e55faa3da5b780ff114d9678ffe", "guid": "bfdfe7dc352907fc980b868725387e98a56efde961b57bdfdc18321e9f4313f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850c0a3f13458dc9ce1bd1e5eac7a5a55", "guid": "bfdfe7dc352907fc980b868725387e9835089cf5d35195bd61833ead80a2c8ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f719080072fe4b497544b3c553e3e188", "guid": "bfdfe7dc352907fc980b868725387e98b7d8e43c80f9252f548b9777689e1cb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7ccaa7a7e07ecaee89f7290edeb0b22", "guid": "bfdfe7dc352907fc980b868725387e985746e71a324b67fbcba55d65f8395141", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9832231dadde76b93a9fee534a0a45e824", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980aa9f05f68b3964076a6200c99a12a93", "guid": "bfdfe7dc352907fc980b868725387e98afcc706be8bcd7b9d3e62522605014cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bab28d463fb5d3d03e44084d401e048", "guid": "bfdfe7dc352907fc980b868725387e98ce9c3b45c667672d9f9fb473a632b7a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b8f75a5e6a587bcc8cf45def06cd0e8", "guid": "bfdfe7dc352907fc980b868725387e988a4e489f2a2eb72114cf502775006b51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ff3e3c906a4169ab7ab383db1a0385f", "guid": "bfdfe7dc352907fc980b868725387e982728865d305b863c9edb834e858c4cc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6822120018a9119cba967123a945218", "guid": "bfdfe7dc352907fc980b868725387e98e06e7fe2588a8ccc6089430fc30366e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e008117575fd5820e45ca0b3af92cb1", "guid": "bfdfe7dc352907fc980b868725387e9881b6b8b06fba0d320446105be2076e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad60c20017a5ce270ad84e965ead88c2", "guid": "bfdfe7dc352907fc980b868725387e98a91701c955ebf330c3ed67dda9349777"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f369d35eaf4fd7d6dc68bea499729e8", "guid": "bfdfe7dc352907fc980b868725387e98af10caf25cb9e8829029049aa1c09c0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ba24e1b04f6e46e08420e62d608f554", "guid": "bfdfe7dc352907fc980b868725387e988870ae7bd408b8569a192865536ec366"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cebe96d2bbfbecd0161b2f7c24b00ff", "guid": "bfdfe7dc352907fc980b868725387e98482aa9ae0f49619d8e0c0465a58b9725"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb999624d7773315bb8049c1949f5eb5", "guid": "bfdfe7dc352907fc980b868725387e988feb011f7c20b77393977fe0f68e1ab8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0ee3001350bdb7ae620023cacfaa740", "guid": "bfdfe7dc352907fc980b868725387e98e558b0fee16bc8776a55e10c83cb7b77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98892cbc1c9dbce03295de64a6c4887a1a", "guid": "bfdfe7dc352907fc980b868725387e98707296ef80e00bfb22344a922f8dae85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8ca7ae79d9cbb40a6b86ec5c1440420", "guid": "bfdfe7dc352907fc980b868725387e98d14b65845ffe2edeecd5b83b82065831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e882adb230515536700534db1b0c4a7", "guid": "bfdfe7dc352907fc980b868725387e985cb195c4b408b791b53fb8c7b09f5325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857c4dbf8e064457a26dd0c991f4f7b04", "guid": "bfdfe7dc352907fc980b868725387e98b09d44d6bf347625bdafb6a06efab1ab"}], "guid": "bfdfe7dc352907fc980b868725387e986d45f22e602faee496d62beb66d96305", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e98d055b93477d0d02a380e16cfb16b642c"}], "guid": "bfdfe7dc352907fc980b868725387e98d23b672e476b4492b02d1bf4bcb08451", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e5d21c55d001cf1bd451cd248d29e82d", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e989b0437b4d4c4589dab7213538ea96ff9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
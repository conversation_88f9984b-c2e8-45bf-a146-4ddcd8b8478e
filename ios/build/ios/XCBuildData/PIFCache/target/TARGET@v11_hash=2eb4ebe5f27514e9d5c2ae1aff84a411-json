{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98646e27d1cddb8a28840403e105120f99", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e3e3a98355c126810a4089d7f52cd69a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e3e3a98355c126810a4089d7f52cd69a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982d12280252702588d7cd7ebb34188947", "guid": "bfdfe7dc352907fc980b868725387e98451271ac92a2a6b3bc220a26c9165552", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985796ceaa60e5401fb107b71373e81eee", "guid": "bfdfe7dc352907fc980b868725387e98803fd33d35f2b4a487541d8ec4e94db2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825294332c0e6e682dfd9e6ec6e79687a", "guid": "bfdfe7dc352907fc980b868725387e98ca13dab421a323308e8ddc6cdb237977", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810671342612597a0338142e7cc137a69", "guid": "bfdfe7dc352907fc980b868725387e9855863e1daf52a474496c62b8e26b1623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8d3a3e4827c83e5027faabaa1078cf6", "guid": "bfdfe7dc352907fc980b868725387e98fbeb2ccbdccf43c8c39119e52627393e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf1bcd5bbd9d28907f90bbf090207d2", "guid": "bfdfe7dc352907fc980b868725387e98de1977ee82e83b08cdd262f30ab1e546", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869ec23e153faa4a637b6dfc52c891ed2", "guid": "bfdfe7dc352907fc980b868725387e98f2ff79ac1b761f0f79e693ae36952994", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888f2f981e93c01bf88f7806850d3f873", "guid": "bfdfe7dc352907fc980b868725387e98f1833bff7e312b1000761b520b229d5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff20ea98459e018ef52c92c8b5e9da78", "guid": "bfdfe7dc352907fc980b868725387e98b7a704666dd231f45c8c0c5039cf962c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874fbfc492b344808f1e148d036b517d2", "guid": "bfdfe7dc352907fc980b868725387e98d254ab7cac101db2e9608be9268e33df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ecd3b896a16ae293c3ef74bbb3ad48a", "guid": "bfdfe7dc352907fc980b868725387e9841995aad82cee4f0d1af9a0970f1d9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98879d4bb6efef1691ac0a3d50dffcf678", "guid": "bfdfe7dc352907fc980b868725387e98f6a6e186019a7b6bbcf7edf80d739ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986db34133c8ad15e53cb40f57bfd0c0d9", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c1d689b21bc847929c9d458fbe5036b", "guid": "bfdfe7dc352907fc980b868725387e9878bf6ea35872ff69d8716d14e8726fb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebbb16b55bc5af0a12adc5b762a2ca04", "guid": "bfdfe7dc352907fc980b868725387e980929dec59f51f260fb14375cd3aee123", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f086990d52543e5b0b0b8b2b8038c2f7", "guid": "bfdfe7dc352907fc980b868725387e98978956dcfaf4dcaa425b470b0ab6591c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829dd9a4a2109c88dbef222d43f07f98c", "guid": "bfdfe7dc352907fc980b868725387e98d3e49fdf20728c8da29875785f93fa34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd9526f1088326876a64b1f239e9537b", "guid": "bfdfe7dc352907fc980b868725387e9875ed083cdffa950109d382aa9bb635a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5f0d443376f16ab173962e812043c6", "guid": "bfdfe7dc352907fc980b868725387e98da892b09f0181aca06faf2f98c1b6abd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b2b3eb1cfbeeaae0d7ba6057d96cd7", "guid": "bfdfe7dc352907fc980b868725387e98e03749eb0dfd6a1bd972248fd565e7f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986790efccb6649be1929f81adaafd7609", "guid": "bfdfe7dc352907fc980b868725387e98ba32cbf0b2f954399f58199c72f84410", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c6aed266cace1a18be4dbc2cc711fe9", "guid": "bfdfe7dc352907fc980b868725387e981ec4f2ee3baa2378c72056f6d51532bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e1aa88c7b6735a647fbb5f2844daca", "guid": "bfdfe7dc352907fc980b868725387e981f1bb3e1716086026593817395ba776c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98826a91a14b2a494604b4345a727db301", "guid": "bfdfe7dc352907fc980b868725387e98c78b17d465f66fc46ff613f796c4b8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98584c9a543fb325db0db2e1426e117d77", "guid": "bfdfe7dc352907fc980b868725387e98e2fdcfc26cf4ff69bfe0979351b649db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986899bd9b3814324b9caf73badc14ae6c", "guid": "bfdfe7dc352907fc980b868725387e98d5c7402eee4379664783cc3196df872b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a231cffd0fa1d33e89a49af92f3dacb9", "guid": "bfdfe7dc352907fc980b868725387e98f2b049120e51de6572d77eb4fa27601b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853b70620299487a92a55b51ab6b49a81", "guid": "bfdfe7dc352907fc980b868725387e98470c0a98e19494a4f7a55af39256cbd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdfb22c482971178fb3da7cee0aeb76a", "guid": "bfdfe7dc352907fc980b868725387e985e260f4f5b1601c288ba7f6d58a3836b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b650495981bacfd92d3753a26f001b95", "guid": "bfdfe7dc352907fc980b868725387e980a606703b1dc9d426d651976283c7afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855816479e8d901955530483bfd41e239", "guid": "bfdfe7dc352907fc980b868725387e98794fa1083299c389c3e06b1da4679eb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec8fbb80b50642f29d636045882aeb1a", "guid": "bfdfe7dc352907fc980b868725387e984f81bf588b7132779cfda37357f5988a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d595f4463c1882edd253952f764e4185", "guid": "bfdfe7dc352907fc980b868725387e983b380180ac8372c476a807b9287534c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be377ba7d52472eded2e9951b6259879", "guid": "bfdfe7dc352907fc980b868725387e9826dbc66437fe08e5f660cd7d57a07ade"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982548d3384ab13ffbfe5c0291a1d8a1fe", "guid": "bfdfe7dc352907fc980b868725387e9848228779c89b7cf4c56f96d9771d2ce3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d8aff2d384d277b28541a0fec3bf47f", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f316c7025a4c93b52e3ae185240ac535", "guid": "bfdfe7dc352907fc980b868725387e98ff0bc9241cfa1703ee578d05d882114d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98583ec42f07b36ebc19c7002b95c839d4", "guid": "bfdfe7dc352907fc980b868725387e98dc21c349147484be8818b0c698c008d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981248c8a7a1c7a65f3f319b6517ae8b64", "guid": "bfdfe7dc352907fc980b868725387e9854ae6e6291c1d1636792ade71a7bed6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe898869a2a2c6e2bbbd09a6920fd302", "guid": "bfdfe7dc352907fc980b868725387e98a76ab6623a0005b7a05de9a09b4bbe0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc0290538ab83b1e228597b314a2207a", "guid": "bfdfe7dc352907fc980b868725387e985233b06bb9e39497179a34ca3d93e52b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f98fb639c3c431a96e7e7c5dabeb46f", "guid": "bfdfe7dc352907fc980b868725387e98bdf453b73e17a86d84100cf68d60e04b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecfdf25fd84aaf6edfe0e6570cd37d6e", "guid": "bfdfe7dc352907fc980b868725387e98700a094c3bb1e173435375a335e28e84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98210aaf41765f930b1d80e1c3e91a70ef", "guid": "bfdfe7dc352907fc980b868725387e981d3f412c09d65d91f50cc28e7385ca9f"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
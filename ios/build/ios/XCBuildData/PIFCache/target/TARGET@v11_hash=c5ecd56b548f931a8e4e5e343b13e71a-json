{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d77ce01b796835e78c0bb0c4a877ce7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98940d7531c027d41d704f224eb4b14957", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982287609c2a6418a9636a3e58aac224cb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fe758e6cf490b7d10c2f8de3a57375cc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982287609c2a6418a9636a3e58aac224cb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9807274e0f5e2b11d9f1c96453e840e8bc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988683b9f8228b13ac1ad9ebd468a0cd51", "guid": "bfdfe7dc352907fc980b868725387e98d1fd72905bbb360cda4c12150c3b6a99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e53f98ec81ced8b706576043e719f5", "guid": "bfdfe7dc352907fc980b868725387e98da164ba332a1d22ad7c516925edf8084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4c9152319fa54c106b273b55f76289e", "guid": "bfdfe7dc352907fc980b868725387e98b06dc0ff395f83db20a6762db228bf31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f0afae225386f204cca6bc730211ef8", "guid": "bfdfe7dc352907fc980b868725387e985733677e09793d0ea43ad3455e34be0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0d96c9027bd42953deb8470e197c9b6", "guid": "bfdfe7dc352907fc980b868725387e984fcfbc856887b1346da3e9a6a3c0c685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98693e750d2dceb8adb567cbb4da804397", "guid": "bfdfe7dc352907fc980b868725387e989b95fceb4fabc18ff6c2ad4130b7bffe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f814f265277eb5bc14c42480c77bd0", "guid": "bfdfe7dc352907fc980b868725387e98a988485fa7647bac908e42684e6142ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f4e5e6f30b42b48b24152350572db94", "guid": "bfdfe7dc352907fc980b868725387e984de506243e236604af668dbc015a1678", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af0c457999b5dfca9a7f7be2b8202315", "guid": "bfdfe7dc352907fc980b868725387e98632114c36a80bcc87acd13b32c06f47b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882250bd94349012238dbf94e6c274d61", "guid": "bfdfe7dc352907fc980b868725387e98dbd8af896941c078d595414ad56b25c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985503d8715cde4892c9e493a9d2b76c9a", "guid": "bfdfe7dc352907fc980b868725387e988e2217168c0b2d6988aa7e651eb549d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fd9c59fe4d1df4e0430ed4257f4f4b7", "guid": "bfdfe7dc352907fc980b868725387e98eaf183b56d373bdcb1ef624909bf364c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd0eade3b82d6670efc59f72ccac1922", "guid": "bfdfe7dc352907fc980b868725387e984b991ec80f8ff8b3a9a7d242b7265406", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813a66b950ecaa3df903d6ebf040c5386", "guid": "bfdfe7dc352907fc980b868725387e98cbd392d44cf956d0e3d62bc8e3a8fa2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f87b79f827c94f28a31122ca9a61401b", "guid": "bfdfe7dc352907fc980b868725387e98e898deceb2036b0c7645f66f120d0733"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e48f63d0abcaa4234c5214cf8a7fd38", "guid": "bfdfe7dc352907fc980b868725387e98773be39fc416e84d33add37c742bdf38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b579da8d2a66445c6fbec12db3a675", "guid": "bfdfe7dc352907fc980b868725387e98c6f27ffa7da25f021b9ff95af13d2a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aaf6dd20fa3a4840b07a02d15c963c1", "guid": "bfdfe7dc352907fc980b868725387e986d65ae6d2da6e4ec34b23d12bacca9eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe4230ae646ac6036d8b688a035817da", "guid": "bfdfe7dc352907fc980b868725387e98b969b9e0a69903acb09e9f957b52ecd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985917750666a4d25f52e1cb35a153b2b8", "guid": "bfdfe7dc352907fc980b868725387e98e7e6a3b6ec1d5d81a28c9a35e5f731bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdc4dec8fe69c2079ee0cdb4ff1cc7fe", "guid": "bfdfe7dc352907fc980b868725387e98dd3fba7fbdd06f4662a1d242f891528d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd621f54d93efd821f0151b853774084", "guid": "bfdfe7dc352907fc980b868725387e987b6fb0921e3e9adce06a257b2ef3ff6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc1935266f3fa0f1d29197cd030fe4e7", "guid": "bfdfe7dc352907fc980b868725387e98f3b0e5e057e01c6cc4b612a5676c22cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986201cba4d245247919b8b972ba1886a4", "guid": "bfdfe7dc352907fc980b868725387e980b2386b743513f20783da914e9d2b9c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878de60fa9b4219bf3135a09bace2edec", "guid": "bfdfe7dc352907fc980b868725387e981f1cead94c2a73782fdfc3c31fc18664"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874fe81b91abb7ffdee0e1b81f2509a87", "guid": "bfdfe7dc352907fc980b868725387e9844a0fcbcd5782ab0b4ed30df8acdd07d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da23ce4a4d55ab1291cf4b52467c0bc", "guid": "bfdfe7dc352907fc980b868725387e9864d7db7494aa0bdb509378e4114f7e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806405b8ec45ea4c07a6b8bbb859bc010", "guid": "bfdfe7dc352907fc980b868725387e9854d93748570ecfed8858c60150cd870c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ab53d0e4cb57d92c74b127f3bac79b3", "guid": "bfdfe7dc352907fc980b868725387e98ef0f8b1314028b969f7ad8c2ae65cad7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1b0f80a643ee70ee7140e68a8b90e97", "guid": "bfdfe7dc352907fc980b868725387e988d8397ef2fa1c300dd8aebbd2f46a4d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98289fae4757ba3d0cd311712deb895373", "guid": "bfdfe7dc352907fc980b868725387e989fec12828d0ae75c13208baf4e9cde3a"}], "guid": "bfdfe7dc352907fc980b868725387e9889ef38a9bbd9de5964e78dc994424d72", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9819e777de0f27057e5c87903fe2afef71", "guid": "bfdfe7dc352907fc980b868725387e9843dfdfb363a3c1d95ad21d6c44603037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e45009881b93457b7af5d1321ce08576", "guid": "bfdfe7dc352907fc980b868725387e9865948f69dec446ae824cb717f0eba4c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2bcf5a7939de8c897037c1a22b0173e", "guid": "bfdfe7dc352907fc980b868725387e9875513272c3bb1b8ef8bdba3a9cd0ca02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcfc3eec27a4ce0bd2155b1d327e80fd", "guid": "bfdfe7dc352907fc980b868725387e982097d68a1a74856e8ba4e8903b87e55a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987de5431142b1d5b8c4cf1b14067bbf0d", "guid": "bfdfe7dc352907fc980b868725387e987fd98ba501491a79507f559dd40d678c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982104851ff637dbab34c9b261b2375bf3", "guid": "bfdfe7dc352907fc980b868725387e9869711937ee3546dd565c0b56fe39fb2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816979394d7666dea83965bce73c283e9", "guid": "bfdfe7dc352907fc980b868725387e98a63d48f9f0ae4d2c563da1604d65bc1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9812d7a79e75870f0043bea64ee6ce0", "guid": "bfdfe7dc352907fc980b868725387e982e2f4858c50e1839fb4838745d1ca846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b7d17399f275cc275c697b12b639274", "guid": "bfdfe7dc352907fc980b868725387e989f7b94bd9b2c981cda2177148ba480b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e849bc4cea4c70ef546fe53ff32709", "guid": "bfdfe7dc352907fc980b868725387e98bb4e0608592e6a98f2397ca09e106aa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f3b756443997e55a9958bcf9ef3a0c9", "guid": "bfdfe7dc352907fc980b868725387e988da464a4019516573845df91f40803ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f7cf445093c6ddd759afeb6e7a8189", "guid": "bfdfe7dc352907fc980b868725387e9824559522466cd124b292009ea49e660d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986293542a204c5bd9897ec8a2a6aa93f5", "guid": "bfdfe7dc352907fc980b868725387e98d5395c7fe86e0d1335213c7a898b4e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987985ef063d4e0813601d6281bb31dfa4", "guid": "bfdfe7dc352907fc980b868725387e985ce0a5ab72a02a6edc2032d20b709f81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981911ca8201147a2101d4acfc1f8be761", "guid": "bfdfe7dc352907fc980b868725387e989b0a12a7fdb15e5285037fe8280171f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4a06b4c9a04a89e0523a3326205481", "guid": "bfdfe7dc352907fc980b868725387e983d0106131b1def0bd6d3c2b8872eaac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da38aaba6494310eeb9a272667c8f662", "guid": "bfdfe7dc352907fc980b868725387e98874ea2192a529edd48815335c2e5db76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eed6c939ed6c513127ee66922dcfdfd8", "guid": "bfdfe7dc352907fc980b868725387e98bc7082777cedc2c96e786aeffc723be6"}], "guid": "bfdfe7dc352907fc980b868725387e98f6cd46367b6b907e14021a9f239731f5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e982f4aa7ed6a3da16da5598319e1c1cfa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c21bfed2c82d49928be2dc564b741859", "guid": "bfdfe7dc352907fc980b868725387e982a3da22f32220631aeff692757884cbe"}], "guid": "bfdfe7dc352907fc980b868725387e9875782a223ba5a82b1935b6953abcf392", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98047f1b93a57e58af297d80662497a948", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e983efc3cbd0837cabf6d2f60c4639cff36", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
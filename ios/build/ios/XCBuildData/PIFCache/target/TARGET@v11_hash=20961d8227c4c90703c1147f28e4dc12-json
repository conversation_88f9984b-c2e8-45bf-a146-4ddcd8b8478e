{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985417a18c365faa60df0625e45de6fbe4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aaab455f6adc35ae5b19222403a5a3c1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d8a1b5effda1cf7a94383f46d068c8b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982b4a2c333552aa6d7d327af66096e1fa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d8a1b5effda1cf7a94383f46d068c8b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b5efacc087b3976828b54171891452aa", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98970ae0a067a74f8d50820b633f2b89b5", "guid": "bfdfe7dc352907fc980b868725387e987dfdc3945818efa157f2abfe037ddcd8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988643d0871830a778658caee60f3c1d38", "guid": "bfdfe7dc352907fc980b868725387e98f629dca1b0d2e0c54c92a67c1af45650", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895161e55c799822f968accfb85da88b0", "guid": "bfdfe7dc352907fc980b868725387e98668e04f80c86f04267cb80cc7db7c066"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c35ccf2f1c53f37b4ab7bdc34f4c65a0", "guid": "bfdfe7dc352907fc980b868725387e98ea4e1944516225425ce0a2fe6d201574", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3e9f6bd81db8e3de0212a2d9fff215d", "guid": "bfdfe7dc352907fc980b868725387e98a971bf67e1378ad0ecc854b46aa704be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3b1ce51d1aea409768f958a6c1ca9e", "guid": "bfdfe7dc352907fc980b868725387e98bf16cc263b20ab7924c8bc9b55c7d749", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5f2a6375d635390a18d20566b2577c4", "guid": "bfdfe7dc352907fc980b868725387e981d04b3ea23b7f410a3ab01df2c2f4220", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839f367bd307aa3d8b73b282554c46dbb", "guid": "bfdfe7dc352907fc980b868725387e98a37fec45c30c0702acabee852e27bf4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e8e29fcfe01772983b6db0ecefc4697", "guid": "bfdfe7dc352907fc980b868725387e98fa294d5d86938fef9a5a2ac100e731b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ecd2b2458a6c08d1d528e50ed4803bf", "guid": "bfdfe7dc352907fc980b868725387e982a90521527193e40f81eb8f81bbab20d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98034e57ee00836aaca14a7d1d800f3d30", "guid": "bfdfe7dc352907fc980b868725387e982c840440d0f3bd09273e1e2b5e5016ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98463e19899367fc1ce126ff9911ed94ab", "guid": "bfdfe7dc352907fc980b868725387e98d28091f8440207494ac3a9babfd88719", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988acdf879abf6bf034208fcb935cceb07", "guid": "bfdfe7dc352907fc980b868725387e985bc7d2b276fad3b550bbac178ce7236d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821403025c6c43088dc9af77d32e68860", "guid": "bfdfe7dc352907fc980b868725387e9846a2e1addb3982c8a64290e2b8656d76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8364622e48d5192aeeaa32f914c1522", "guid": "bfdfe7dc352907fc980b868725387e9853c72706cb3886cd992d3ff5de2a7375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865b9f1848b7363c740a222ff91c11393", "guid": "bfdfe7dc352907fc980b868725387e9857be1d75c3620591af15c6aea678da14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982958a4f78c7f64d76dd29b9239ec6638", "guid": "bfdfe7dc352907fc980b868725387e98f57e7501d2816057a8d0231952adbfe0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193214b5ed1fdba98c2e46f858478641", "guid": "bfdfe7dc352907fc980b868725387e98695bdfb3602104ee1107b2ee5f5c85d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd3005f8719966c2cd9857b1c8c5453", "guid": "bfdfe7dc352907fc980b868725387e98b54e2ae1ffe7203271e43a85184e8719", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e93c8a49ca7f0d724cbc781f23b3e02", "guid": "bfdfe7dc352907fc980b868725387e98de999b2a658704cf50c54b587a5b0174", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831a6214015468b5bae4cb0704bc40447", "guid": "bfdfe7dc352907fc980b868725387e98a317be15cf1eaf197f8ab81cc254749a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f46c9f5b034aab88c5a7ae77d72b7a4", "guid": "bfdfe7dc352907fc980b868725387e98ddbeea48aa1d5f6f3c164e6861234436"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987182c5362811ea5ee52408526da39bbf", "guid": "bfdfe7dc352907fc980b868725387e9809042ba8f73645107f15cce54dae9f36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf7901f8031ed816529c38c086cb9db4", "guid": "bfdfe7dc352907fc980b868725387e98565d5abedc91dbb3595ebead7deff9ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819d5da13f65b989f8f963b96c73ad95f", "guid": "bfdfe7dc352907fc980b868725387e989804b2c6d9a4521a9eb7328fc8c30923", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e7891157f6510fca722583ab97b0bba", "guid": "bfdfe7dc352907fc980b868725387e980cf354e7d6846a89e700884614d6127d"}], "guid": "bfdfe7dc352907fc980b868725387e987f6503822c4554b4822b04632ac4febf", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aca867ee2634d92e489065cfa0c44267", "guid": "bfdfe7dc352907fc980b868725387e98cd47851bcb272edadd3ae252afb90a0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98708d6cd4dca8a1a7c34e8f52533a77fb", "guid": "bfdfe7dc352907fc980b868725387e98eefbae81f1b56b63d55b0bfa0cf1ebf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98663943a01e59c5a892a1336c549417ab", "guid": "bfdfe7dc352907fc980b868725387e9823679a108d9fdb415d5a34a4a7acbcaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e3c0b00ffa31a7c83bb7516d0ed913", "guid": "bfdfe7dc352907fc980b868725387e982ab4cdf3317aa70b914ffd875a8cfd69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aa2ae3bf664317873d8fdfd8644baf7", "guid": "bfdfe7dc352907fc980b868725387e9811cd65926a91a12b329c56f2a3a894d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98364a8cb9ae090d57d28f9d0ef658b8c8", "guid": "bfdfe7dc352907fc980b868725387e98d7eeb70b8d47ef2e4f1ed43d9dea6f66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7dda4280787caf5ed9bd3cd4a18a5e2", "guid": "bfdfe7dc352907fc980b868725387e98a867f8e6c4183a5c329d8f80a9fc538f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e025a8467607cae3af8dd2b8e91ca20", "guid": "bfdfe7dc352907fc980b868725387e988ff6fd25a48a2a192f3862d817df1a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6e83564b39026215f5a5803ef74d295", "guid": "bfdfe7dc352907fc980b868725387e9857dbe74445c773d8650e64f37b77ed30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362339b1e60a7d3830a7169b624c82d1", "guid": "bfdfe7dc352907fc980b868725387e98e4d2ee0ffee4402e0c7a8f996b0599bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d63b1477202368eca18b5deb3eb18848", "guid": "bfdfe7dc352907fc980b868725387e980343e75446e7fb2e51416049a201650a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a50819d2eae3bd3a3896b186068a8961", "guid": "bfdfe7dc352907fc980b868725387e98a5dadc12c156054eb18f9182a5bb2504"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a357bd69b0a9399a75489743fe7437dc", "guid": "bfdfe7dc352907fc980b868725387e980648cd09abd38e97eadee2b2873e0e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ee67f5fb881734dd6e34fbadd1b7b8c", "guid": "bfdfe7dc352907fc980b868725387e98bf9a5e296505cd7bb052cd05b4dd3c0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee1baf432ffb9b830e6d32e0ba0c2975", "guid": "bfdfe7dc352907fc980b868725387e98858b48b92d265674c9be0fc2015a1a60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894867a791f6055f28faf5731a0bfa745", "guid": "bfdfe7dc352907fc980b868725387e98f89e6eab7f28157eed49a9be3ce72521"}], "guid": "bfdfe7dc352907fc980b868725387e98a7781879f83b44103c4e794ff87656dc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e983d3e3cf83c6dc6cbf9dc755ce73b73b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98893e4f43ea38aaa1079f62389548f89e", "guid": "bfdfe7dc352907fc980b868725387e989cf2372247e91cf97f2622e71fb23bda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984696e9bfad1bfbbb855166c1ed115504", "guid": "bfdfe7dc352907fc980b868725387e98c8a42954c1cb0f1527e816d75b75efa0"}], "guid": "bfdfe7dc352907fc980b868725387e981f6b43badee8ab40a4d7f9abdbfe09df", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98bd3b957eabecb59248e1daded091ef12", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9884fa9eb7ac981c84bfc5394e6922d5c6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
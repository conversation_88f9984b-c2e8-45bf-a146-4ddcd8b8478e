{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985b0635dc3684c6339b359b8b8a6b4582", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fc9bf04e8ac578ce181933ec592bc8a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b24e01a1c0c5fe717f0f368b997a963", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c98c86fff114e6efada9cb43e93ab64", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b24e01a1c0c5fe717f0f368b997a963", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810a621892696e4715518a64fd308fbe1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982579bbd71017f4938e59761cb2082bfb", "guid": "bfdfe7dc352907fc980b868725387e9848f2c86709d2311f205f49c727b0d39e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe093c8a70b6f12998ed4d01d298f7b", "guid": "bfdfe7dc352907fc980b868725387e98908a5db620e5051eba6ac1eb387f2004", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c292c337581e8aa78ad5fae037eb5d9e", "guid": "bfdfe7dc352907fc980b868725387e982456a0e41f9bea73123090ffd3bac243", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ccc15e5b5e22faf53d636cc5c110efc", "guid": "bfdfe7dc352907fc980b868725387e98f5a391bd6080ea8683c2b81b6f3c64e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acd74a72c00b0c76a79e92b214ac0a10", "guid": "bfdfe7dc352907fc980b868725387e98d0b1b00c08626777438a43be98e3ea11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d763149106d39b7f2c1c4b18062ef7e", "guid": "bfdfe7dc352907fc980b868725387e98ad1a5ee2e1a0f228a78f2fbea696fc2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceb085f8b98ac491989d9c2bcfe42842", "guid": "bfdfe7dc352907fc980b868725387e98d21ee20ec5b17cf609f4a326d11e3afb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98474b08e9ab5a61e66c42cd01b031c512", "guid": "bfdfe7dc352907fc980b868725387e98436bd0fa5fc431d47288ada295eb36dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98222fe11ce5e424f1de60b93cd6c47da6", "guid": "bfdfe7dc352907fc980b868725387e9861849a2692c9f35cc36b8d5e71d17f9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5494e451018ca0ef8994fb3d0e2c1b7", "guid": "bfdfe7dc352907fc980b868725387e983d3aa624ab60c1077e0c7c2bba51d53c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988685842663dd4e1be1ab483120115ee7", "guid": "bfdfe7dc352907fc980b868725387e98ca867af2ebf73aaafc4b2fcd90dfcbd6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4454afb3efe95e60b15293495750ee9", "guid": "bfdfe7dc352907fc980b868725387e98a56efde961b57bdfdc18321e9f4313f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a8f2eeda61d35c12a64972b8ce46450", "guid": "bfdfe7dc352907fc980b868725387e9835089cf5d35195bd61833ead80a2c8ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827f182588fd2b89c4fa8cc61e5f7fdf1", "guid": "bfdfe7dc352907fc980b868725387e98b7d8e43c80f9252f548b9777689e1cb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c64e01fa80dd1bf6764ade52bc47b80", "guid": "bfdfe7dc352907fc980b868725387e985746e71a324b67fbcba55d65f8395141", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9832231dadde76b93a9fee534a0a45e824", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98faf6510afb74935c773d5e06f35013d8", "guid": "bfdfe7dc352907fc980b868725387e98afcc706be8bcd7b9d3e62522605014cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f56ef0dc5979fd9b2f987d61385b4a26", "guid": "bfdfe7dc352907fc980b868725387e98ce9c3b45c667672d9f9fb473a632b7a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853f3c8d05ec89331c6e5bc78e34b3779", "guid": "bfdfe7dc352907fc980b868725387e988a4e489f2a2eb72114cf502775006b51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cbb3516286d6040860229b9934fb47b", "guid": "bfdfe7dc352907fc980b868725387e982728865d305b863c9edb834e858c4cc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834656184e832525e1c321f2d1e123c48", "guid": "bfdfe7dc352907fc980b868725387e98e06e7fe2588a8ccc6089430fc30366e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851b3b5747897d768ba2c04578dbe3149", "guid": "bfdfe7dc352907fc980b868725387e9881b6b8b06fba0d320446105be2076e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d12412fd95c22bf5346337ee318046", "guid": "bfdfe7dc352907fc980b868725387e98a91701c955ebf330c3ed67dda9349777"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea8f2058a0608c1ce5572aa9209f8183", "guid": "bfdfe7dc352907fc980b868725387e98af10caf25cb9e8829029049aa1c09c0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5fc454813613e1ec0437e8c6cd16512", "guid": "bfdfe7dc352907fc980b868725387e988870ae7bd408b8569a192865536ec366"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b42bd1c6e598d0e2a19718b6501d7d", "guid": "bfdfe7dc352907fc980b868725387e98482aa9ae0f49619d8e0c0465a58b9725"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809f4f06a6b75cdf272888896817ebb07", "guid": "bfdfe7dc352907fc980b868725387e988feb011f7c20b77393977fe0f68e1ab8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd50f26da4b14b3b0a59660a46411426", "guid": "bfdfe7dc352907fc980b868725387e98e558b0fee16bc8776a55e10c83cb7b77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c883ffaae51001a72e75c2b725a9cea", "guid": "bfdfe7dc352907fc980b868725387e98707296ef80e00bfb22344a922f8dae85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986000329f9c8b1a023a53d33a559dbc43", "guid": "bfdfe7dc352907fc980b868725387e98d14b65845ffe2edeecd5b83b82065831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806bde857e33ac51507559d3316d3f650", "guid": "bfdfe7dc352907fc980b868725387e985cb195c4b408b791b53fb8c7b09f5325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8fecd5149e725aef57d13a6d16bce6c", "guid": "bfdfe7dc352907fc980b868725387e98b09d44d6bf347625bdafb6a06efab1ab"}], "guid": "bfdfe7dc352907fc980b868725387e986d45f22e602faee496d62beb66d96305", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e98d055b93477d0d02a380e16cfb16b642c"}], "guid": "bfdfe7dc352907fc980b868725387e98d23b672e476b4492b02d1bf4bcb08451", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e5d21c55d001cf1bd451cd248d29e82d", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e989b0437b4d4c4589dab7213538ea96ff9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a22c5ccbf0285c9d4d029bfcf737f5c1", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981015e59b6bb1a1abc61da5e0536817f3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860b386ca9a6c361bcce2b17825120267", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e54aee575e117c2cbc57b72ce788ea7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9860b386ca9a6c361bcce2b17825120267", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983f6ee91e0ed336f80c42b5efb71c155f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983adc6e23a5ba748b9c943b5d3a9f136b", "guid": "bfdfe7dc352907fc980b868725387e9835602f642053378fc771e188a6cd2644", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8ee57d5cff36a634e63fdf6e4bd5a05", "guid": "bfdfe7dc352907fc980b868725387e98bc739902b5781b0981d2a9d3bae516e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab49806a3b16e096e492fce87887cd5", "guid": "bfdfe7dc352907fc980b868725387e98282c5321d90fdfa80c7c721e1fe5747b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce5800d759d1931909018c161ce3a926", "guid": "bfdfe7dc352907fc980b868725387e98417aabcf586011e5b92318f8c8ae6f75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1b761ad06a32437f9b8bda59d5b9443", "guid": "bfdfe7dc352907fc980b868725387e989062522680049ae7d7a650710fc620c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a864a21fb004301ae6350d29ad79e24c", "guid": "bfdfe7dc352907fc980b868725387e98389495cf917b06b7159f78302d0c839b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7b14ddcdb5b67a5ee22061745f3b004", "guid": "bfdfe7dc352907fc980b868725387e98f144202d8f6e40ea35733b1b2eb3b96f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801f96cf36663ff878d8bd0193a5b579e", "guid": "bfdfe7dc352907fc980b868725387e9893b58a001c21258bf613091560a8dc32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b725f9d76fd8b8f43fdf7a56a3fbec28", "guid": "bfdfe7dc352907fc980b868725387e98b7371e2b59238a683a24719eee8605a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98811193f95bac02037035314a4bdfeab7", "guid": "bfdfe7dc352907fc980b868725387e9813161d9cf851affd005d44504f39af08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c155709aaad8571d9ab69795e72d987", "guid": "bfdfe7dc352907fc980b868725387e98d65c2ad6d834678a9913db8510afc4ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98694b3493dab7aab0f789dfaaf10a36b8", "guid": "bfdfe7dc352907fc980b868725387e98dbd5cf3472748d73c92e329e24230b44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98963c9e7cafcc352b2f76a777ea35e4c0", "guid": "bfdfe7dc352907fc980b868725387e98a94e220683e869f641e8dac45e1dd3b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb02e8e3cb39ab4f63a3c8a7868e1408", "guid": "bfdfe7dc352907fc980b868725387e98e4dade83b1730abb7cbf15ae4328772b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987448f0c33f60703feefa8d99d45448e4", "guid": "bfdfe7dc352907fc980b868725387e98a1f7ab8d288aec12e400851b6f3a8145", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f776e448fbea1c8addcd871103c4f14", "guid": "bfdfe7dc352907fc980b868725387e98f8ebeec8deccdd69cc70865c90c6133f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cf147a02f8ecba06d13cd7b6fec373f", "guid": "bfdfe7dc352907fc980b868725387e988a2cd8dbcd66c37885566caf178b85f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e1198c52d7a12e1c32bd51574bf4bcb", "guid": "bfdfe7dc352907fc980b868725387e986527dd402125f3aa4b93ae11ae1b7fad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984be90e26dc8865dcc3ea5f9092aeb058", "guid": "bfdfe7dc352907fc980b868725387e986c1a2f0cdfed48cb85dd9ef2e3fe632e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885f24ca394d0041109a70e4b18e26457", "guid": "bfdfe7dc352907fc980b868725387e98b74e14c42218791f751f067c763b1774", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c8f5b201917959d4f59fd1cdb8ea31", "guid": "bfdfe7dc352907fc980b868725387e98b57f9680f4799577e6e079319e9efda2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98352d2807168721f787cf27cb372fd5b6", "guid": "bfdfe7dc352907fc980b868725387e98a4604b801e15e2b656a59de1830982cb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986f8951416fe5fc517e6e9b0b2b034cf7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9814ae8603b154e8dc1c35523a1aae21f3", "guid": "bfdfe7dc352907fc980b868725387e98885b52aec46062b805893c0d1c870cb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b44dd7aadf9a1e8cea1d62598deecd7", "guid": "bfdfe7dc352907fc980b868725387e983acb7fa5d240ccb7e43b73b4b2a33b00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6a8e6df6b376e4ea8ee33d72e9a19ce", "guid": "bfdfe7dc352907fc980b868725387e98ee2a3d88109672f4dd1f1ee89e295555"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894deec6a0df612a31b5ce6449a9d5aaf", "guid": "bfdfe7dc352907fc980b868725387e98085dd410f6641682d002eb6d4299614c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98720714de2e0db897302b294ca08ccde4", "guid": "bfdfe7dc352907fc980b868725387e98fe62df6bec7eb68f49940ad103447ac6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3050e366b8a80dd9fb913101510e8ef", "guid": "bfdfe7dc352907fc980b868725387e980b67012140ff9ea445298e161ae1e98d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803571f52d202315b2a94b580f84436c5", "guid": "bfdfe7dc352907fc980b868725387e98d9f1e610cef0619f150a6d0a517a54f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816d2cb87c350ffd2dfa66dfb105d56eb", "guid": "bfdfe7dc352907fc980b868725387e986107a70c9b2ab8c3bfca0f18dde8179f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b16602ba71d9bf6e6496417cbcf5033", "guid": "bfdfe7dc352907fc980b868725387e98938656e969b708b0c467e0a9a545e682"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989621ea8106a4a04eb24415c006dc128f", "guid": "bfdfe7dc352907fc980b868725387e98eeceef3489f6db34deec742cc8869477"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9fb5f15ce987915b5c9df4218944f7a", "guid": "bfdfe7dc352907fc980b868725387e983df84008a288227755ffb5cdcf228936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b37e36ae9190ef0c242faa32d53b44e", "guid": "bfdfe7dc352907fc980b868725387e98353e67b7fe60aa038ec91541f8c0dca0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5927e170365e3159f7d56aa5ed1f7c2", "guid": "bfdfe7dc352907fc980b868725387e98fdb12ba5f115bb409eca749c3eeaba49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc97f1801a7d475e3fb45c99a179803", "guid": "bfdfe7dc352907fc980b868725387e9821bd28be2b1fd1a8a736d872f1ec58c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883be47e86b6cc47dff13d5b31e5c9ad0", "guid": "bfdfe7dc352907fc980b868725387e98781adf8ede79f7378b7aea62cdabee7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a95a9cd1cf11c175fca3e73505892d", "guid": "bfdfe7dc352907fc980b868725387e98f5ccbbc0925dfccb51fa388feb851a20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad82f48bb94713de3fc05b080ac81c06", "guid": "bfdfe7dc352907fc980b868725387e98bbd16ffc17bb4e60825c3c8ba2e3254f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd9927b2f5f9a89f4b27edf87cd29b3", "guid": "bfdfe7dc352907fc980b868725387e9887bdcf5ffb7ce2d300e3abf9b4b8ef64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989177ad6eaac9b9d9672f09d2a4b19cf0", "guid": "bfdfe7dc352907fc980b868725387e98ea24d7183ece70748591b8cfab78cec5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ff31a686a582de196175ea3bd815168", "guid": "bfdfe7dc352907fc980b868725387e9845ee2db9b9eae708ec81193b9b67380d"}], "guid": "bfdfe7dc352907fc980b868725387e98921dfa19ae580a6edc6173f16bbee363", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e98d67fa85eac13569597931ecf3234bd12"}], "guid": "bfdfe7dc352907fc980b868725387e98d2e41c6ce71e4968c4d789260cdcde6e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b60c0f767075a100ba9886947c0e9f29", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98cb212bf1e39b241b64b2400204f09b78", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
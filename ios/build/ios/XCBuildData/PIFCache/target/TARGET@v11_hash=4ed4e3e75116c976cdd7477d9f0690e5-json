{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989f06ba7863a476cc7480b4004f50889a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bf44415bfe9117765ec71ba1b490ec9d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b9ef4df0975dd79e07326c11b45588f1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b14da6e61b93b433b6c38509da372978", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b9ef4df0975dd79e07326c11b45588f1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b8e3c461d0791efb0d8262bd6406d0c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fdb11844d33cf1609beb59b246c08f40", "guid": "bfdfe7dc352907fc980b868725387e987f3a5961957f2012d68382423094c1c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98141bef8b7d67740cc724ff922744d23c", "guid": "bfdfe7dc352907fc980b868725387e981904ab5509f93b301357f8b443fbd7e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98879de2b7a7541afcccfd058d938dd5ee", "guid": "bfdfe7dc352907fc980b868725387e98b673b99c8b68016c6a21ce10f48c1928", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841d13afb9ceb76f3807d52c5bad6876e", "guid": "bfdfe7dc352907fc980b868725387e9842d80b7d3e8450d9bdc7ed6d3ca6b680", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c58ab6ee22919052b8186fac681c7abc", "guid": "bfdfe7dc352907fc980b868725387e989d5363a8db08c615238839d337607029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800b2f88f6ebe95285506d36399341bb4", "guid": "bfdfe7dc352907fc980b868725387e983b5b9d8083067792b0e450b9ff9013c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a049c7947c22278b0b365cd1c3804dd", "guid": "bfdfe7dc352907fc980b868725387e98079b3005f242694def30c2e5030056a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be4dec9c9b4da69bd27b612bc776f714", "guid": "bfdfe7dc352907fc980b868725387e98dc99c6d9aabcb3ef9fcf9b633f239c52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f347b0ee767264f24e8f4d0b0d74d5cd", "guid": "bfdfe7dc352907fc980b868725387e989b376a5026259d7736b7e85cb7b78e96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb9dc786ad86f5c129c7b3b5aa1c9d67", "guid": "bfdfe7dc352907fc980b868725387e988fe153cdd47086403236babae449f84e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdbaa6f92acb42183149154b3c777c2e", "guid": "bfdfe7dc352907fc980b868725387e98550d448cdfe5c0c9e2f1b0fdb10bc5c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a45322825bd68bcd7477f3f8997c22", "guid": "bfdfe7dc352907fc980b868725387e9807cffea146e6c88b87f1439bf84d9bff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ff7770ecb84d0cf3eff4028d9871947", "guid": "bfdfe7dc352907fc980b868725387e981208aab9e3d67bd76456f1b100ca8e93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f850686c11e3d88ac6c4496288695105", "guid": "bfdfe7dc352907fc980b868725387e9836c1f48313801a5534b8dddbebd5a179", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b49827eb7be08eb9a82e4bd0e228f3", "guid": "bfdfe7dc352907fc980b868725387e984de67ed09fc776fe905237256c3d8c56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db456710d10f03009864dee0ae69edef", "guid": "bfdfe7dc352907fc980b868725387e984fa12c8860f9b9b2909953cfdebceea9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c354922981f37968fcd779da6a481f7", "guid": "bfdfe7dc352907fc980b868725387e989f120ef8247586ef4cb19481e6eb9061", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6916f6851581474936dd83c302d9050", "guid": "bfdfe7dc352907fc980b868725387e98a17782a8a0e8368d0e7e5072d13caf64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b754f11b3feb9e13d67e94ba8df36f", "guid": "bfdfe7dc352907fc980b868725387e982baa8a3436e42e0e65f72f8ed9094d2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e59f35c3461f3455a98de8bbdc0ddfa", "guid": "bfdfe7dc352907fc980b868725387e98b306d513071786356a713c8edef608b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcbd6dfd305c3b268ad9af0a843120d8", "guid": "bfdfe7dc352907fc980b868725387e98d11013825e15c2e1f919dcbe6abffbcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988301aca08d99c626c22eeafc1cacd03b", "guid": "bfdfe7dc352907fc980b868725387e98b43686e64661750324b2e76c8c00ca4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6e56e7b51b56098554127bad263bb64", "guid": "bfdfe7dc352907fc980b868725387e98dbf7ee03e93c0eed0f7fd6984881303b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cbcc303bb693f36dc386de878886fcc", "guid": "bfdfe7dc352907fc980b868725387e980b0333145f4f0bf821399eed84f5ca6a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98446ad082b609386c426f11ef4ca54e13", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9893a26beb4b4dcbd92b6cfd8f1c9889d2", "guid": "bfdfe7dc352907fc980b868725387e986be835e67d27a54de1d7bcfe07048eb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d0058a5fd8ca521153b455669b732c", "guid": "bfdfe7dc352907fc980b868725387e98727358c1cf04f5218a959efad9ef4443"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c4482082aca75830f75cf78d34d0556", "guid": "bfdfe7dc352907fc980b868725387e9867ab75c1745eef4020cabc7aa7b91a8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4676fca3fe22db698ec62555efad7ae", "guid": "bfdfe7dc352907fc980b868725387e98c8834844081a482add578add6b515968"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb50fe7c31487a63b31f937bd433d98f", "guid": "bfdfe7dc352907fc980b868725387e98775c4d617affb6303f2f90cd3a8d1f45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e33d1e50b2f2db1f95e026994c895bc1", "guid": "bfdfe7dc352907fc980b868725387e981c4a6614906908153cd2705d5113a6ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c00523e69e8b57b54de1c4b151f39856", "guid": "bfdfe7dc352907fc980b868725387e986cf4de2ee5b14bebd674478bf96e83eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989286074969d12edce8e54dcedbcf58e7", "guid": "bfdfe7dc352907fc980b868725387e9833c3736414151917515868ef769cf4b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e9385f7c4e5ab4a5e95c16c2cdbabc7", "guid": "bfdfe7dc352907fc980b868725387e982ab68bd8639bbda4ea730eaf1d77bfd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858d7f946ab4b5ca216cf705d4ca014b2", "guid": "bfdfe7dc352907fc980b868725387e98ecffe84fcf8db36bc222ed01bee6c94d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98663c458b65827884e995dd85f18e7534", "guid": "bfdfe7dc352907fc980b868725387e983ee32c92339cca3ca809dc183db61fc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba8c1504bab6d02c35f5966989e1291b", "guid": "bfdfe7dc352907fc980b868725387e988021cfbe058e9f46dcda79bf50d4ec4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3dae715a77c4668bce9a79032359449", "guid": "bfdfe7dc352907fc980b868725387e98e923d6fc2afbdda053246feb2decdef8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987223876440f7d007d8c85cb18422184e", "guid": "bfdfe7dc352907fc980b868725387e9803d62c03cd7f452060c97c5ff53e2566"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fa974b35ef5ae144bcfdf8178adf807", "guid": "bfdfe7dc352907fc980b868725387e98e7abf64016ccdb1284849a49b6574d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e295ede6653169b90f5a4f1a44ba9adf", "guid": "bfdfe7dc352907fc980b868725387e98e03a778d319a7c1b7897113c717030ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854ebc1e2231a64bdfea729783fe16fbf", "guid": "bfdfe7dc352907fc980b868725387e9870b67b236676ba63b367849210cc4361"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a589ddcfbbe4d3d75185dcdd57be34b2", "guid": "bfdfe7dc352907fc980b868725387e98f7ad144704ce5994e7240ab3e8dedcf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f599f6c084cf16cb3fe2de8b3120100", "guid": "bfdfe7dc352907fc980b868725387e9826b2cf7813ddaf4aa49b9144d48c10e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98648a2b4e4c65004fa3c3d962fe8ce7e9", "guid": "bfdfe7dc352907fc980b868725387e98c9e91fe83a8cf665f774b397748bb021"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818748eac770d2021996b851e276b8ac4", "guid": "bfdfe7dc352907fc980b868725387e98874a15c47eaa8097cb08b2e1668a7260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac93755dc17a338cb5c90e34eff96c10", "guid": "bfdfe7dc352907fc980b868725387e980b53de977cd085041d88fa223a2c1a03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b3d6753ecefbfcde609846f829d4f50", "guid": "bfdfe7dc352907fc980b868725387e98c13028c4f1c9d0cb3064492f0ab09af4"}], "guid": "bfdfe7dc352907fc980b868725387e98c2f14f579cafabbe591aaf70d3ad68bc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e986f16428b13d8b62c1b9c22de658922ca"}], "guid": "bfdfe7dc352907fc980b868725387e982f0d5a5d768aaa148581ac052f301e8c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d8aaf0d2637a10d30f9aebdb3687ef6e", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f92c5f7a354bfd48b5753cdd06512f3f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
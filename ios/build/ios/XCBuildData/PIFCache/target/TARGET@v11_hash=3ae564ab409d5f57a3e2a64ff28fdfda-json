{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98df1ebd80dfe4ea134767bea1488c7012", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/app_links/app_links-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/app_links/app_links-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/app_links/app_links.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "app_links", "PRODUCT_NAME": "app_links", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fac96d5808bb3e13aeaf6e6317c4a6d3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986eb166de6815a340c950719c90282bea", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/app_links/app_links-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/app_links/app_links-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/app_links/app_links.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "app_links", "PRODUCT_NAME": "app_links", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f4ecd816fcb79c9ebe3b5f763debd772", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986eb166de6815a340c950719c90282bea", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/app_links/app_links-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/app_links/app_links-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/app_links/app_links.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "app_links", "PRODUCT_NAME": "app_links", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2aca84ed9ca150fcda2314997fb489c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983d291cab92e9620892201bee52f2c91e", "guid": "bfdfe7dc352907fc980b868725387e98b91003090def0e3653d29f4eb19c116a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f04f0eb8c3db806c4d895255788e12", "guid": "bfdfe7dc352907fc980b868725387e98ce694075d51b07d9e93abd09bcbe5467", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ff66a828ef97da4b83e9cb22bf39a8f7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f12b407a3ae34bb561543e01ec3cf5bd", "guid": "bfdfe7dc352907fc980b868725387e9861c023b16aa513ce565a6788a9c4c404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825554394e894859fca1ef150964774c3", "guid": "bfdfe7dc352907fc980b868725387e9880b56adc7e6cde405359bf0f664be315"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9717a57d488245b5d0dbda946ab8149", "guid": "bfdfe7dc352907fc980b868725387e98829983ccb5dd58d03b56e24c39467b6e"}], "guid": "bfdfe7dc352907fc980b868725387e985f20b77e854167ba54067251c07f654c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e98bcd7f3aaf1c31dd7157912b473a57003"}], "guid": "bfdfe7dc352907fc980b868725387e98801ddda7248cdcf26f803abf58675785", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9866f10fce738afc1ea2073aebd89bd14c", "targetReference": "bfdfe7dc352907fc980b868725387e9854f2b496f3d836ac3fd0e138cbba7daf"}], "guid": "bfdfe7dc352907fc980b868725387e98e83dc441d66ae205a4d5a3fcac55df0f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9854f2b496f3d836ac3fd0e138cbba7daf", "name": "app_links-app_links_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98b7a881c3b0dd7865fc7e59ca8d94706c", "name": "app_links", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9803641e6d929e76d8631aa821e6f5f498", "name": "app_links.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
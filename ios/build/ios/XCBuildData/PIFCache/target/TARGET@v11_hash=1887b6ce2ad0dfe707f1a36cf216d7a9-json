{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982aa4132265a6328f13243fd9cef5b52a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98262f37f1a0d2389c7aa489ec6e99f5d4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837262555d7a8522bf433f7f9553836da", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98af200b288f5254818fdaa786f1388ed3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837262555d7a8522bf433f7f9553836da", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c3710dc4958e6fd0e5a6bb45e6401b6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98afacbb9b9ca467b2a6d0cadb00aa0a22", "guid": "bfdfe7dc352907fc980b868725387e9841dbf101e44f0795d9e3432d5a9b248b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e505318a7e479d3f0782c91d301dba43", "guid": "bfdfe7dc352907fc980b868725387e989fb3c6bdacc23e1821e091ba6e1b66c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883c472307353d04621040c324cea5458", "guid": "bfdfe7dc352907fc980b868725387e9837e09791cea283affb15eccb66440911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98374f36350ffc21a27b8fe149a765f627", "guid": "bfdfe7dc352907fc980b868725387e98423d66d9c4fe8bc2e162f69b2023c693"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987145c656bdcaea08104239f5d12aa846", "guid": "bfdfe7dc352907fc980b868725387e984806696853f420e9c43186e3bca5a9bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f90bc3fdd6b99b536ef465395528105", "guid": "bfdfe7dc352907fc980b868725387e984caebdb333592754ed6de7d17800feb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a06258749c01e844ce447f0f230314e", "guid": "bfdfe7dc352907fc980b868725387e988741518cbb01df2152ccce5047623da6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea521f400fab7db5769d00c6190014e", "guid": "bfdfe7dc352907fc980b868725387e98cfb73aae593e3e193d38abd934c78b37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7c766e5cb9b2c18411bd91af3cef970", "guid": "bfdfe7dc352907fc980b868725387e98c1f421a1ddb1cc40cfd28e07d810eb44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8d8402b7a5dbd11ff3ddb59618c28b", "guid": "bfdfe7dc352907fc980b868725387e98396a1ca88e238b15ab7d3f17cbb07cf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cda58ffb33e0acb635e268e8af86a4d", "guid": "bfdfe7dc352907fc980b868725387e9806897ff87cacd605f21df280187aaa27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f6b46684b5c627c8d070a26ef57f7f", "guid": "bfdfe7dc352907fc980b868725387e985c77204fb2d2e751a2c3a0c96a1323bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804e3686a1517507b145eabaa43961317", "guid": "bfdfe7dc352907fc980b868725387e98818ec7d03f36a8bec909065a4f2f019f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e48a8a5204e99f95512e144a5ee31d23", "guid": "bfdfe7dc352907fc980b868725387e980d2da2ad91fae0c35715aeca55dde926"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0e58feb79ce79261ca210c2db9709e5", "guid": "bfdfe7dc352907fc980b868725387e988527c6071014a06b351ac4f1bdbcd3d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834da128390d0e3751b1539491caf792d", "guid": "bfdfe7dc352907fc980b868725387e980877185e6e3afd25adf79f7c67de443e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98806d6af5333de1e531b5b7969cea441b", "guid": "bfdfe7dc352907fc980b868725387e98d0dace231a419d3fa28bd0553d5aa859"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6928495ca4ac1d80ab2f76e4ba7a86a", "guid": "bfdfe7dc352907fc980b868725387e983bd394476c2d22352050f7c3d4c2472d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c52e53aefb58778d907af4570e3bf2b", "guid": "bfdfe7dc352907fc980b868725387e98e7cfef89e2923403d1455e27b1ea57d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1845d7a80fc3f35fc1f3d8159a56995", "guid": "bfdfe7dc352907fc980b868725387e98f1b48c322077d6ec868e665517bfac2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808483f62fa26381691b1678590a3cb78", "guid": "bfdfe7dc352907fc980b868725387e98a4aac64af03873c367d4661a8a97ca69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f998e6fd0814d6ac61c60eb383ab1f", "guid": "bfdfe7dc352907fc980b868725387e98bec34f46e553f98524c924f78cdccb61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a0b91b674b98d21183e8844ae80385", "guid": "bfdfe7dc352907fc980b868725387e9851e7710219fcdf433b49f082b32ba17f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98523990fdfa7c7bbba3c8a92ac4b4120a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9832562dbf496589355792a46129460aa5", "guid": "bfdfe7dc352907fc980b868725387e98e219170e4567b6afb7e85bef89cfba05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b49bfaf712d546a2b8b193f108c3ef9d", "guid": "bfdfe7dc352907fc980b868725387e98341d863e40ddb8860a6e3cf349148884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834b0fed48248a93fe916b3ae45fefc0f", "guid": "bfdfe7dc352907fc980b868725387e9804693c2dbfe22677d0b0392da67a560f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2380bbfbe351757161a998220a37a8d", "guid": "bfdfe7dc352907fc980b868725387e98aafe41a9905064890a9ae254defecdcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb37fd8fdcc06a2dc1a2b80befe6787e", "guid": "bfdfe7dc352907fc980b868725387e98f6ab0d408810bde675e124c33aad7d59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e36880d1744383df2a11456cfb332026", "guid": "bfdfe7dc352907fc980b868725387e986c79895e8ebef0efaa4ec24e9063a9ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a9ada9c56222491b7cd07a0b62d30e4", "guid": "bfdfe7dc352907fc980b868725387e98cff9eacdd11346df03c5ce55cfc4af11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b329835a785226dcc8b1101cb2d59957", "guid": "bfdfe7dc352907fc980b868725387e9847b47e3ac5185c2e2a6a886517f5a343"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982967862ce619cca97f1d135b877f6528", "guid": "bfdfe7dc352907fc980b868725387e98407a5b24729502a1512f1762a0ba6a1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6eb03aaeed80f69682a627786810261", "guid": "bfdfe7dc352907fc980b868725387e989b55639f9529a250e1e550fc997c7385"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a7d6ca1a067df162b205a209d3c037", "guid": "bfdfe7dc352907fc980b868725387e9872ea9875e2ce74c098f21aeca3715ee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cc868834fc68c9942e7f918ee0d147f", "guid": "bfdfe7dc352907fc980b868725387e98a5b6bf04f7182c7d1342076cad5b76b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b31bb1f8064a37a19dfbec2d12ddbabe", "guid": "bfdfe7dc352907fc980b868725387e98bf1f5af551c32dde01ccde108a04c3e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98517413d07110cf6ee571eae2304fe660", "guid": "bfdfe7dc352907fc980b868725387e98ed65a0e917b3d224f494cc4d1848c5a5"}], "guid": "bfdfe7dc352907fc980b868725387e98cc0eaf06f328a0c50558e53c109e6d5a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e9890359e829ebc545865e891cdc1ddf929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a27a22e6958412f527d1f9caa36dc969", "guid": "bfdfe7dc352907fc980b868725387e980711c59476f042b9b608b4ae5fe52533"}], "guid": "bfdfe7dc352907fc980b868725387e9815bffec23238566ca819a3cc8d22b4aa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98448d4415f878a6f5e842b4fd50d637a5", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98ff0eecebf3f4c31dcf25a60d7844fc0b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
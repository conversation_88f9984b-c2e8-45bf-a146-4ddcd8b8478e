{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4737d83053e9290cdb2a77d5c915fca", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9815cc14bca5f22fcbb0d729905dbc8e25", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984dae33788bf713be87bfe0a91a4e4274", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987bbf4e9ef1157aa7a75be0741530d5a5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984dae33788bf713be87bfe0a91a4e4274", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98dff58b348a8cad1be345ad6642ce5197", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983241c93ef19dd0d54f651d50d2a83998", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e8b25855def9096adb0e9a6879800d1c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d4ade5e43d80d88ad25961e370977531", "guid": "bfdfe7dc352907fc980b868725387e983c6a301b467de4120f9f14b61884399a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873907a4a45898846be5df9198785ef1d", "guid": "bfdfe7dc352907fc980b868725387e9876e63e19fc190f4132813ed4488339f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6b399e5230f6506a23f74b371e86411", "guid": "bfdfe7dc352907fc980b868725387e989dc190fc7154663eae0f6c3ca4acb79f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837306ec9c2265a14ad5b19996d1827a9", "guid": "bfdfe7dc352907fc980b868725387e981ef200b9bbcd5c3f908462ecb8037f62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f15872cc245fe6ef91badb2bf75a3bf", "guid": "bfdfe7dc352907fc980b868725387e984b525eac6cad15798788b0585bb0859f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfc5fa4442b38cb4aa778a019e1f6f26", "guid": "bfdfe7dc352907fc980b868725387e988cd9b51820be76deb836d5bef8939e27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851d3b0ec830beb370e3fdb678b34ebd3", "guid": "bfdfe7dc352907fc980b868725387e985680835cd7f9e8e523fb6e3a2964f0ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75e3d1fd8ee5242f76865e4e816b6f3", "guid": "bfdfe7dc352907fc980b868725387e981d51fb47f8e3b8d09eb562ffd42e35e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e11dbd4fc70832864d6c7abf0278ac87", "guid": "bfdfe7dc352907fc980b868725387e985cc31957b832760db7e104e5dc66c35f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98746e79c2c0795fadabf414fd5b5ac870", "guid": "bfdfe7dc352907fc980b868725387e98368d555535be3032c65a8fd12fa2c718"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a7221463fcac28a0c17766acca7a407", "guid": "bfdfe7dc352907fc980b868725387e981b6518d25fafa4e5b423031388f3aa61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813c005830baac32b965677a0abb1b292", "guid": "bfdfe7dc352907fc980b868725387e98c1bfdb76988d36ed09613ba22eb20b91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d96c5400647e80f3656b35e9284cbb5", "guid": "bfdfe7dc352907fc980b868725387e984bce292a91cc2c7c9fa78121f71d087c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d879e505c1d98066b44c0316cb00bc98", "guid": "bfdfe7dc352907fc980b868725387e988c6daa60b9093364950fcfbeb639d13a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e54b88ff0fff67f7081d326a939bad", "guid": "bfdfe7dc352907fc980b868725387e9814d59740602f1e44421a0ed323bae5c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803eac8e20c558f8bc458243e0b562d37", "guid": "bfdfe7dc352907fc980b868725387e98a6c913182cee36a8c237f4d57603f830"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f281b3fd0e94a9f3aff4bb965ce8020", "guid": "bfdfe7dc352907fc980b868725387e98e610722eaa00b129d46130e392c69ab0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835d7041541e7dde9af36aece1870b2ed", "guid": "bfdfe7dc352907fc980b868725387e9891e27cb04696905914d0ebddc8a33b88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daea1d7b2bbf594743b28abe3aae489d", "guid": "bfdfe7dc352907fc980b868725387e989ba976dbb3fa135846f33146feafcff3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d078f0739153ac63868488a2a4fa17", "guid": "bfdfe7dc352907fc980b868725387e98d5230d9cc458096b6b5246376ab68a6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d92932c6f97f0452c2efe03f54ebcc38", "guid": "bfdfe7dc352907fc980b868725387e98f629fd75437268b703f36292b1f57e14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ab4f38864957e52868ace55ace6d1f", "guid": "bfdfe7dc352907fc980b868725387e98b2313954b75bef44feac94cef60c815b"}], "guid": "bfdfe7dc352907fc980b868725387e985b3dc868aeb1de45e27027b4b63e7c49", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988238c136bfe4506963163e44fae4b14a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1f8693f7c908f5b0affe1f241e6c220", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c24c068b057b1ca3beb287b70e4ad2d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b731d65cc4406511fff066edd613cb3e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981c24c068b057b1ca3beb287b70e4ad2d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d26b1adc32d733906901b1009d9fe42", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987774a3cb9b8e5443c9b192f334494fd1", "guid": "bfdfe7dc352907fc980b868725387e9826d944e7e5dbe34563446a3e758e3c97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984007836ed8442927876159d1657fd020", "guid": "bfdfe7dc352907fc980b868725387e98a7fa45549d850eb8c72c903f1d4df534", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d10172817df5af66ac69744bf4eaac35", "guid": "bfdfe7dc352907fc980b868725387e98c9980ec40e14e533520302c0addfc129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98485a296bced6d2f886578cca484ca068", "guid": "bfdfe7dc352907fc980b868725387e981a0253ca8236b2356ace6497f390b8da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821880937e6d729c39b3de023aca7c64e", "guid": "bfdfe7dc352907fc980b868725387e9883f423328b710e16048e6c1e36f95f07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98974a3cb2ab07431d4c78c19e252a467e", "guid": "bfdfe7dc352907fc980b868725387e98ab72dedf3bdd3d652c3e6660af0d44df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd125909b7a8268e0dde6be6d11700d", "guid": "bfdfe7dc352907fc980b868725387e985e560f967fc36d3f5792db6210cd8d7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803ca1a9bde108c9a0862d6acd6bdd774", "guid": "bfdfe7dc352907fc980b868725387e98ee8d700f8fdbed195242f6232fa9edb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988955b2d26b11c00abb6fbb4b5f3f0829", "guid": "bfdfe7dc352907fc980b868725387e98ebfbb7e756d1ebaa66324a532de8abdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da7618245498a3627aac8728c7e64789", "guid": "bfdfe7dc352907fc980b868725387e98db4cbd66691a075dec236f2f049f783b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2dc20cea0f532da6e2140f63e4f4d05", "guid": "bfdfe7dc352907fc980b868725387e98f4fdd91f6659afc648c05e57c4edf8e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98353844db9f4ab2b7d10b689ffbd9cd88", "guid": "bfdfe7dc352907fc980b868725387e98d38d7209dfbeb30917d103def304e045", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcb9561e259567ab4dc311291c8a3940", "guid": "bfdfe7dc352907fc980b868725387e98e1b835fe1d12f123de44830b9d39ef33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801afdf7b20e52fa2d27a38a7cddb3c97", "guid": "bfdfe7dc352907fc980b868725387e9897f4d8054d146697b89e9ae95e6cab1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853bdf15aa30ab6b6cf642e4549413a0d", "guid": "bfdfe7dc352907fc980b868725387e98f4cae67c92706a04a914adb1f102c5d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b75bef7c7a32ea3a63cd52c73ff5c34d", "guid": "bfdfe7dc352907fc980b868725387e986da3806c1c15c1c19d947c614ab658bd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9891b6c87882cff834a6d4722792d27722", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c999d60929dc61ba814fafe6a3a028ca", "guid": "bfdfe7dc352907fc980b868725387e980e81720d61a3801d969422a07b3694f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebc8e5cce4f986a8f4614982dc53b85f", "guid": "bfdfe7dc352907fc980b868725387e9852677058a7640559c4feadf4e3c0c5f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98054be2be02a221c7afec340ea8c6d70d", "guid": "bfdfe7dc352907fc980b868725387e98f9e123e91d169f5077b5b4c09138b1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984783fa098596ad345f76177c490a6cfa", "guid": "bfdfe7dc352907fc980b868725387e98b4dc059e1d83e87ebcefe43ba94df0f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0652ebbec4a175c71932a843a992f66", "guid": "bfdfe7dc352907fc980b868725387e9861a80f10186a44162739a66582e34ac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980399d56f4ea3d38805724b0b2a55e15e", "guid": "bfdfe7dc352907fc980b868725387e98321228c8645ec8c337f06a4080584f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d08d7a777731222b9884c3270f7fa51d", "guid": "bfdfe7dc352907fc980b868725387e9876e2fcde8e9bf49d940acd65e1dee028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f4c68f4d336266a7f2d4d3580520940", "guid": "bfdfe7dc352907fc980b868725387e98127082dd44fec19b84a6b82a9671f1ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a0d8ee7779ce30333dde49c0922ee14", "guid": "bfdfe7dc352907fc980b868725387e98e7358517f8700c32c40012ae498f65d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a0b8c7d918b120c96ee3bc70f79c33", "guid": "bfdfe7dc352907fc980b868725387e986e96d52f7011c1a80218cf8d88c5daf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c3e32335ebfa102504d3ecfa9aa7261", "guid": "bfdfe7dc352907fc980b868725387e98c2c33a008b1dfed23a9d7d00602717e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820a8e2e29f394754a7aac87b793fc6eb", "guid": "bfdfe7dc352907fc980b868725387e9807681073bd833184cb12f4453cb4aa18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838d26a9f0ab89cdd7e1d7ca4894ed9d2", "guid": "bfdfe7dc352907fc980b868725387e981992c38dfd126c35ec1d145b29914f16"}], "guid": "bfdfe7dc352907fc980b868725387e98448c8389dfc199dac83239b7c4d4a87d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986a42ad16868deef2bc647140f437ecdd", "guid": "bfdfe7dc352907fc980b868725387e98f30217f2638041bc05fbf64d6676681c"}], "guid": "bfdfe7dc352907fc980b868725387e9837666ca8f131a9306dd950b0c4ffecf0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ceec10663e715758ba9294abee964c51", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
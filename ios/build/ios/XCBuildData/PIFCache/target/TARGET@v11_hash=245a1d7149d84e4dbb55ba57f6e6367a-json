{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982aa4132265a6328f13243fd9cef5b52a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987e467344679ac962b31d01d8493f95b5", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837262555d7a8522bf433f7f9553836da", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d05b98fda6f4bc7f7a79323dd76ff24", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9837262555d7a8522bf433f7f9553836da", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d22e1e746ceaf07e9bd5305c63ceb129", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98afacbb9b9ca467b2a6d0cadb00aa0a22", "guid": "bfdfe7dc352907fc980b868725387e98c8bdfeb331d7ed57789b1ae46d8e1348"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e505318a7e479d3f0782c91d301dba43", "guid": "bfdfe7dc352907fc980b868725387e98cea8f744c913464e390639da03c9f9ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883c472307353d04621040c324cea5458", "guid": "bfdfe7dc352907fc980b868725387e988b5c5e228644d37b3d49ad84480b2500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98374f36350ffc21a27b8fe149a765f627", "guid": "bfdfe7dc352907fc980b868725387e98b3a6789d579d988314d7b28d0cbbc347"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987145c656bdcaea08104239f5d12aa846", "guid": "bfdfe7dc352907fc980b868725387e98acc45a4a01f1784dfcf858f5213c2595"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f90bc3fdd6b99b536ef465395528105", "guid": "bfdfe7dc352907fc980b868725387e98e3fcc8158ad11be11b0e9e951b4478df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a06258749c01e844ce447f0f230314e", "guid": "bfdfe7dc352907fc980b868725387e98ed41bd93a85710d53df963d144ac21c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ea521f400fab7db5769d00c6190014e", "guid": "bfdfe7dc352907fc980b868725387e98133fada2ee27cd7b4295e0b301781a43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7c766e5cb9b2c18411bd91af3cef970", "guid": "bfdfe7dc352907fc980b868725387e98706475bd1e3e2a7a85ce1e62fa46990d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8d8402b7a5dbd11ff3ddb59618c28b", "guid": "bfdfe7dc352907fc980b868725387e98e9bc6f43ebcf202899600a378cbcb0af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cda58ffb33e0acb635e268e8af86a4d", "guid": "bfdfe7dc352907fc980b868725387e988b9d907a9fd2573d43445b828c8dcc9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f6b46684b5c627c8d070a26ef57f7f", "guid": "bfdfe7dc352907fc980b868725387e9870cb5cdec0c35b64eb3d2dc5be26f2e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804e3686a1517507b145eabaa43961317", "guid": "bfdfe7dc352907fc980b868725387e98b42e01b0b4deb967416779bdb7089df4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e48a8a5204e99f95512e144a5ee31d23", "guid": "bfdfe7dc352907fc980b868725387e9807df20690ed76088890155b800c86ea0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0e58feb79ce79261ca210c2db9709e5", "guid": "bfdfe7dc352907fc980b868725387e98be4a6f0be594f952ccc5936e6f9ae572"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834da128390d0e3751b1539491caf792d", "guid": "bfdfe7dc352907fc980b868725387e9889f6e720bb11b8941e52a5e997281dd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98806d6af5333de1e531b5b7969cea441b", "guid": "bfdfe7dc352907fc980b868725387e98da8c3296d270a786ec9517d9db105bf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6928495ca4ac1d80ab2f76e4ba7a86a", "guid": "bfdfe7dc352907fc980b868725387e988b8243c9f16333a37d27bd442288c58f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c52e53aefb58778d907af4570e3bf2b", "guid": "bfdfe7dc352907fc980b868725387e98633c2aa611d5ae9837360f7d886bf35e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1845d7a80fc3f35fc1f3d8159a56995", "guid": "bfdfe7dc352907fc980b868725387e98073e224657ef718b6d36f0f2b1fc7244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808483f62fa26381691b1678590a3cb78", "guid": "bfdfe7dc352907fc980b868725387e985beb12ec436e18bf3ce8343160c47daa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2f998e6fd0814d6ac61c60eb383ab1f", "guid": "bfdfe7dc352907fc980b868725387e985268109fc60df4e5d2eb75be9c871990"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821a0b91b674b98d21183e8844ae80385", "guid": "bfdfe7dc352907fc980b868725387e98e897f4ecea6c9eca27b0c175e1f5eaf0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c36826689eafa1892590af5db47b92a6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9832562dbf496589355792a46129460aa5", "guid": "bfdfe7dc352907fc980b868725387e987611255d3c42e550e7caafb51863f147"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b49bfaf712d546a2b8b193f108c3ef9d", "guid": "bfdfe7dc352907fc980b868725387e982237c7cbf10e2b3cd6b5016267fdc7a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834b0fed48248a93fe916b3ae45fefc0f", "guid": "bfdfe7dc352907fc980b868725387e985604880edc2ac3c2ebc604e6fb368671"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2380bbfbe351757161a998220a37a8d", "guid": "bfdfe7dc352907fc980b868725387e982df96f121ea7286cce4e06174767ee96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb37fd8fdcc06a2dc1a2b80befe6787e", "guid": "bfdfe7dc352907fc980b868725387e98e13b1b0045d730fc2c6f5e9c31f48dbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e36880d1744383df2a11456cfb332026", "guid": "bfdfe7dc352907fc980b868725387e98d63f6c478c236ee752d3e22dc2de7143"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a9ada9c56222491b7cd07a0b62d30e4", "guid": "bfdfe7dc352907fc980b868725387e982b75f94320c154a802cee2b3bd673308"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b329835a785226dcc8b1101cb2d59957", "guid": "bfdfe7dc352907fc980b868725387e98abe91c64dc5d4797083ce89ef5fb3193"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982967862ce619cca97f1d135b877f6528", "guid": "bfdfe7dc352907fc980b868725387e98c1eb58cfb5710fb7b55c4b1edb8286f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6eb03aaeed80f69682a627786810261", "guid": "bfdfe7dc352907fc980b868725387e981a1786c40a94b7b227d3f9da9307d6a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a7d6ca1a067df162b205a209d3c037", "guid": "bfdfe7dc352907fc980b868725387e98bc25d290e08f156878fc564dae5d9184"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cc868834fc68c9942e7f918ee0d147f", "guid": "bfdfe7dc352907fc980b868725387e98cda22c23c8af12513ca67b2834f9b9dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b31bb1f8064a37a19dfbec2d12ddbabe", "guid": "bfdfe7dc352907fc980b868725387e9842fdf630b9d6d44abd6e36ff78093878"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98517413d07110cf6ee571eae2304fe660", "guid": "bfdfe7dc352907fc980b868725387e989b077a764d7e4c69a92ff7732a94af2c"}], "guid": "bfdfe7dc352907fc980b868725387e98b5578e71a8fcded274c22b7df357bd5e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e984e22cebb2e95fed577a56f48f5b02d13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863219d4e9a53430111b16a16d60de60c", "guid": "bfdfe7dc352907fc980b868725387e98a596c55245f78d00c7f35d07973b5134"}], "guid": "bfdfe7dc352907fc980b868725387e983b50ca5ad24e2155f11a703f07525fcc", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9889292d516672845a692e12db80822e41", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98ea76b6ceea15709c7eed9e20dc7708de", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
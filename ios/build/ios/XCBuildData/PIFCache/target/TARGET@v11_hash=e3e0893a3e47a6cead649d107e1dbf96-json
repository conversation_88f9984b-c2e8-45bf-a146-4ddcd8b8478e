{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4737d83053e9290cdb2a77d5c915fca", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c1a49d2eb4928f38153fbb752b43f0d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984dae33788bf713be87bfe0a91a4e4274", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a061109697a6fecb5c4052027830873", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984dae33788bf713be87bfe0a91a4e4274", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a2bfc0afebe00a97dd280fbd3b086c7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ca79cf196eb98475789e84f542e4857", "guid": "bfdfe7dc352907fc980b868725387e98305321bb0f6c61d76ab6f8f834f9e1bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989c7318c71e29c5c2fa728fab937f84e4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9892b732bcb3904580683f12834428e3b7", "guid": "bfdfe7dc352907fc980b868725387e986b9c07eb90d1c19fb7ac9f978eac2cfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853a4015c1316dff4dca1c64c9c7a4f67", "guid": "bfdfe7dc352907fc980b868725387e9899bf2eef86ec86d3071d0464ba311bc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884104da1c7bbb10ddbed9f9a5181b1a8", "guid": "bfdfe7dc352907fc980b868725387e989923225760583f9044f3477d68370fb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1d4f9950e13dd69a0f691dc08c85b88", "guid": "bfdfe7dc352907fc980b868725387e980b8fe6bd9ce7b7f7f9b672f675ac40ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98652b9659d84cd071f71804b54a709fde", "guid": "bfdfe7dc352907fc980b868725387e98c7914d6db7a604491a406b7ffd04ffdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845eb83627a540de0c90f397e7d6c1e3a", "guid": "bfdfe7dc352907fc980b868725387e98eb2913c855fd2bba90610341d1005093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c53abb2450a14aea170e937c56f6f75f", "guid": "bfdfe7dc352907fc980b868725387e9818bf3e3f4e2e4ff6409fd3de93a3e67b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98825e3c745840413157dec8731ce83fa2", "guid": "bfdfe7dc352907fc980b868725387e9862f3f56aa2a92efeb6d501703a17fb78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6baf0fe74b6e6736d1e14cb750b357b", "guid": "bfdfe7dc352907fc980b868725387e986f0a5a4d4d049699b38b834e5127e27c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986105ba76966694129bf181f5ca2f316a", "guid": "bfdfe7dc352907fc980b868725387e98b11d52cb23817759d43df3901c9dfdcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986639414bc71402a0a3a295c71faf73a5", "guid": "bfdfe7dc352907fc980b868725387e982e2cfaddbe60c31e36e71f4cf7b78cf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98759a4897171864fb4ec8669e3adefb29", "guid": "bfdfe7dc352907fc980b868725387e9880b17e68f279e7d7783a51e9bb5287b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98841cbd1fb1bc5203ae719dc4d8b83f58", "guid": "bfdfe7dc352907fc980b868725387e98edb05713e1e170990070d795fbfd9ad1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ef45fdf345fdc3d67dd5b65742b3a12", "guid": "bfdfe7dc352907fc980b868725387e98571373774a6c562d2a57eeef6625aad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a4903b53a9f9fbab79bc4f0ac1adc30", "guid": "bfdfe7dc352907fc980b868725387e982b8ddf46d054df8e6dd54a4248a7bf7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dff583c690192d3f9d2c2c6407f0d512", "guid": "bfdfe7dc352907fc980b868725387e98a0be07ca2e426e3bc60d30898d361288"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ede9abf23b719268e948cd719c538781", "guid": "bfdfe7dc352907fc980b868725387e98b3f4cf0af8ed4ae02ddeeb10e435cd75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ba1c0bc36cbe74a325c9d9f383e131", "guid": "bfdfe7dc352907fc980b868725387e9812c712ccbecb48d4ce26f86166ff880e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672fb3bbb602291ffdf26fc0dc7f9312", "guid": "bfdfe7dc352907fc980b868725387e982a440638f7cb9965d090ab0c181da156"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a27abc9a2213b0bf76ba26118b2d2b88", "guid": "bfdfe7dc352907fc980b868725387e9803b6b2b356410bc7bcfcc1b875c69472"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c127bd3db15c260cc9b6f1f95768acf", "guid": "bfdfe7dc352907fc980b868725387e98ec9c4546923a074af807de40344c27c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b45149c4a7a0beabefe94b62627919b", "guid": "bfdfe7dc352907fc980b868725387e985cfeda817f2be498febfcb296540639a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c3dcd53380711b6d80ccfa2975aa8c8", "guid": "bfdfe7dc352907fc980b868725387e982082b1aebfa678d630e6c739c18a5003"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da18b5c183e53e9ab9e6cd62f2387e5a", "guid": "bfdfe7dc352907fc980b868725387e987c4d107baf407af054f8312cf0ad4178"}], "guid": "bfdfe7dc352907fc980b868725387e98239213265a3abaa90df058a8c1bb91cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e9840e570f7d28f64055b1dab9dc749246d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a4dd600f511ecfb97b2973cee56fc7b", "guid": "bfdfe7dc352907fc980b868725387e98192e2db6ac44b70d83d52bc9a34503d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863219d4e9a53430111b16a16d60de60c", "guid": "bfdfe7dc352907fc980b868725387e98f0a88717dff9f0a44bc37f82f0d7d1f4"}], "guid": "bfdfe7dc352907fc980b868725387e98819fd04ebca59115ef049e04b7b72408", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988c2f740ef6174a43ddde75c8d424f6f2", "targetReference": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3"}], "guid": "bfdfe7dc352907fc980b868725387e984c4b1b52a05e595bcc1230a93d57ff06", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
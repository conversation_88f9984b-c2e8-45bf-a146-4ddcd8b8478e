{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b02f590fc0902ec529d16ef791e3c5c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Yams/Yams-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Yams/Yams-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Yams/Yams.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Yams", "PRODUCT_NAME": "Yams", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c039ee23c948a07d0d0efc2b81137dc0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0de1a33dcee030010d798971a47ff76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Yams/Yams-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Yams/Yams-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Yams/Yams.modulemap", "PRODUCT_MODULE_NAME": "Yams", "PRODUCT_NAME": "Yams", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9861cd2c30d21c9230a2b54e7f90ca831c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d0de1a33dcee030010d798971a47ff76", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Yams/Yams-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Yams/Yams-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Yams/Yams.modulemap", "PRODUCT_MODULE_NAME": "Yams", "PRODUCT_NAME": "Yams", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.8", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98617058fbc086ed919637a26cc29415ad", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9806425ecdfef38f92676c526742414658", "guid": "bfdfe7dc352907fc980b868725387e9814a513346db670eba984c4eb61c7b86c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a252c6befe4d62f1c9ebf6e17839ba77", "guid": "bfdfe7dc352907fc980b868725387e984da78f1d1d3a5f3ec5acd853cfe189ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885e7b2dc719c7ce6d008372eeac40a28", "guid": "bfdfe7dc352907fc980b868725387e98f0e4586d9d6a472231c5e61ede6db816", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898291a82382243f4b1cbdd37c6b16b0f", "guid": "bfdfe7dc352907fc980b868725387e98caedd4e113ac120f2e0d20faa8bded4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b69fa857406ff8ad3c4e2a925e78041", "guid": "bfdfe7dc352907fc980b868725387e989ee7fc0f4aabe3cee83107a1869875ae", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b7e263a3c71d14bbbf62de43920da620", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d7a9de58e63e33b18e3fe230e028d416", "guid": "bfdfe7dc352907fc980b868725387e98ed0a47420565b982d207196e62c58086"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e6147c9feef1436791c4bb547266401", "guid": "bfdfe7dc352907fc980b868725387e982f9c027db5611bb23d6ee24046b0b41d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da4e74be948c67e7cc53d8ffd4b238ec", "guid": "bfdfe7dc352907fc980b868725387e9837b55869805bfc36119bd01429e2e76e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eed3650420fc9a7c41a53defad24c6a0", "guid": "bfdfe7dc352907fc980b868725387e987d470940523e854564a680cf4ae0130d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980679d763bb151b0a087dd49d2a4b4abd", "guid": "bfdfe7dc352907fc980b868725387e981b04c7153f2a5185bf49b6c463a87f9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da9a0dae448cc95acc5ae9d860f0126a", "guid": "bfdfe7dc352907fc980b868725387e980f93430acdafd49366b6c498e0b76739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7cbbdce676cc1f9ffd65b1fb0a9a77c", "guid": "bfdfe7dc352907fc980b868725387e983a33022940adc7a72da20de351a2e44f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8845e781e87314d75e6091d70047bd", "guid": "bfdfe7dc352907fc980b868725387e9816f97aa31f97e64de339d0f90ec1385d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b2a9de2bf31f5d9a86b9e2d10aee2a4", "guid": "bfdfe7dc352907fc980b868725387e9811ceb96d6b1d8eb472307befbce190d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ddd18314d831a0f1d566242e8e92916", "guid": "bfdfe7dc352907fc980b868725387e98629cbb4f91fc3ab6ea050ab916728a9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98887d3fe133b1cdf275b93647c4a3d240", "guid": "bfdfe7dc352907fc980b868725387e9850bca9dd1bb7d6e63a23fddb8299f10a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adc4ce7eda9fd812ca6b45a4fa28f4a7", "guid": "bfdfe7dc352907fc980b868725387e98f05b0e904853173dc3167f0464194077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e366ec49b1d20d34606726362fb3d840", "guid": "bfdfe7dc352907fc980b868725387e98796d8d12d1d905c8c2a090a54ef499dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d74d6f9364a8a59b6b316519924382b1", "guid": "bfdfe7dc352907fc980b868725387e98d37b59e0b17a09191b451aa5ffb27b4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495075ece30d47c9b83728d0a060650f", "guid": "bfdfe7dc352907fc980b868725387e9867db45ceb3d52dd0f66984d80efd63ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988276f8a2d3b5d36c5b348b6a7f3d07c3", "guid": "bfdfe7dc352907fc980b868725387e983910590632169299e62d9c387f3a8a43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffb980192edc242bdde8e412a27fdd61", "guid": "bfdfe7dc352907fc980b868725387e984ae1ece8fe36a1f92864e926133c9337"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981de1123e5006fcac6bd69ced339c4c0b", "guid": "bfdfe7dc352907fc980b868725387e984598244644114b6fcda8bf7a09e8019a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e34c88ec71c5ac4cfea87189b5bdb6", "guid": "bfdfe7dc352907fc980b868725387e98ebe095607d259dbc00d4a3c10a6711cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ce745c026b8d382ac429d37c655447e", "guid": "bfdfe7dc352907fc980b868725387e98b4daf5c2443019515618b7ff6b3ef879"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc3126e69003ce059bd4b92795805f5", "guid": "bfdfe7dc352907fc980b868725387e98e16d93afe54a2eb8dbd965da3a1a3ecc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3dfefcd3b54b87012cb0ddf88eca001", "guid": "bfdfe7dc352907fc980b868725387e986a635c55a0491986b2b52b2153126f36"}], "guid": "bfdfe7dc352907fc980b868725387e98d322e35b9d74ddf81f16567e429fa50a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e9812dd137d730d442c95ae02e9c9a13c12"}], "guid": "bfdfe7dc352907fc980b868725387e9872fae1d4c55771c673488227363ecdb9", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c565e68f9c7ecca1751b024a694df1b6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985906e783cd19a852cc9239174f1ec1db", "name": "Yams", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983573af10de618087816a99161b763dc1", "name": "Yams.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
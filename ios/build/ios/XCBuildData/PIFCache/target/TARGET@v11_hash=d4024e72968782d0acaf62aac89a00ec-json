{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f4c21604ca8906af236aff3596b0789", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f1f8693f7c908f5b0affe1f241e6c220", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a2fb377fabcca5c73e89c530f9e4778", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b731d65cc4406511fff066edd613cb3e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a2fb377fabcca5c73e89c530f9e4778", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d26b1adc32d733906901b1009d9fe42", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ce0a206663669b0a2393ff860036dc2f", "guid": "bfdfe7dc352907fc980b868725387e9826d944e7e5dbe34563446a3e758e3c97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981209eefb4e95c41f86ebb26e8e81cbef", "guid": "bfdfe7dc352907fc980b868725387e98a7fa45549d850eb8c72c903f1d4df534", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abb2cd1d936ea706a345f29356e89b16", "guid": "bfdfe7dc352907fc980b868725387e98c9980ec40e14e533520302c0addfc129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986509cb278641fd1ebf3e4cbbd753b41a", "guid": "bfdfe7dc352907fc980b868725387e981a0253ca8236b2356ace6497f390b8da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed62e7477ac890d96a73ca90316a2e6c", "guid": "bfdfe7dc352907fc980b868725387e9883f423328b710e16048e6c1e36f95f07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c87fbf9f73533b8c45cb6974e6f0a8", "guid": "bfdfe7dc352907fc980b868725387e98ab72dedf3bdd3d652c3e6660af0d44df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895a9a90b20d7c3723eb829ede38ba1b9", "guid": "bfdfe7dc352907fc980b868725387e985e560f967fc36d3f5792db6210cd8d7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2d2b0d5eb10d2207b7c97ddca7a14fe", "guid": "bfdfe7dc352907fc980b868725387e98ee8d700f8fdbed195242f6232fa9edb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985837c445f42933977082a30894792589", "guid": "bfdfe7dc352907fc980b868725387e98ebfbb7e756d1ebaa66324a532de8abdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98195d6aada857e68434d89e0739484b24", "guid": "bfdfe7dc352907fc980b868725387e98db4cbd66691a075dec236f2f049f783b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b030ef8e5184fd3553d45e75ebc24a", "guid": "bfdfe7dc352907fc980b868725387e98f4fdd91f6659afc648c05e57c4edf8e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98387d3470d5465efd8250f7085df79940", "guid": "bfdfe7dc352907fc980b868725387e98d38d7209dfbeb30917d103def304e045", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78a7f9f5c27e3316b18ec0d92ea93f7", "guid": "bfdfe7dc352907fc980b868725387e98e1b835fe1d12f123de44830b9d39ef33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830029b0f681c300623f4f7007b8f56fd", "guid": "bfdfe7dc352907fc980b868725387e9897f4d8054d146697b89e9ae95e6cab1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cd8d9eb2d4566f11fd4b4385ae47f52", "guid": "bfdfe7dc352907fc980b868725387e98f4cae67c92706a04a914adb1f102c5d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c9e44b990f0891b2a8ea1a38c764b9", "guid": "bfdfe7dc352907fc980b868725387e986da3806c1c15c1c19d947c614ab658bd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9891b6c87882cff834a6d4722792d27722", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e603b1c342c12b6455e7f6c2e4dd30f9", "guid": "bfdfe7dc352907fc980b868725387e980e81720d61a3801d969422a07b3694f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816c37dcfce098f7b74926691dcfb456b", "guid": "bfdfe7dc352907fc980b868725387e9852677058a7640559c4feadf4e3c0c5f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b9f7be3a8285de71b5da43636748dca", "guid": "bfdfe7dc352907fc980b868725387e98f9e123e91d169f5077b5b4c09138b1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c77342bd9de36e74668cd1cae233f0ad", "guid": "bfdfe7dc352907fc980b868725387e98b4dc059e1d83e87ebcefe43ba94df0f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884fc6d8158473ade2c20a263ba33d65a", "guid": "bfdfe7dc352907fc980b868725387e9861a80f10186a44162739a66582e34ac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bcbfd8e9251e1f70b832c03f9b5d1b5", "guid": "bfdfe7dc352907fc980b868725387e98321228c8645ec8c337f06a4080584f23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d82fd3cc222e28318db7e72243473e1a", "guid": "bfdfe7dc352907fc980b868725387e9876e2fcde8e9bf49d940acd65e1dee028"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c814ea99f7f81d407c39019881f06904", "guid": "bfdfe7dc352907fc980b868725387e98127082dd44fec19b84a6b82a9671f1ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c47ee60ad5b5dc510fff2244699c01", "guid": "bfdfe7dc352907fc980b868725387e98e7358517f8700c32c40012ae498f65d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda54ebee7129fdbdcbdf8405e15453e", "guid": "bfdfe7dc352907fc980b868725387e986e96d52f7011c1a80218cf8d88c5daf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503ef8626cd01f9b983b777246acb691", "guid": "bfdfe7dc352907fc980b868725387e98c2c33a008b1dfed23a9d7d00602717e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b3560e49a42424b3f15b4810a90b83e", "guid": "bfdfe7dc352907fc980b868725387e9807681073bd833184cb12f4453cb4aa18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bffadb450fd95988a2037c8088afb32", "guid": "bfdfe7dc352907fc980b868725387e981992c38dfd126c35ec1d145b29914f16"}], "guid": "bfdfe7dc352907fc980b868725387e98448c8389dfc199dac83239b7c4d4a87d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e98f30217f2638041bc05fbf64d6676681c"}], "guid": "bfdfe7dc352907fc980b868725387e9837666ca8f131a9306dd950b0c4ffecf0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ceec10663e715758ba9294abee964c51", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876d3bd135eed557e44ed96403fae0a51", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f60f21207bdf48370aadaa7a5944056e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988e74cfcfe74f4b456ebbd6064f7c8f8e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca83b02fd91b34a498c4c1d10d117548", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988e74cfcfe74f4b456ebbd6064f7c8f8e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_mobile_ads/google_mobile_ads-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_mobile_ads/google_mobile_ads.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_mobile_ads", "PRODUCT_NAME": "google_mobile_ads", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d212063812730634d67c1838935e42c2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ebc190789115c8954133b4581df8f640", "guid": "bfdfe7dc352907fc980b868725387e982164fc4c60916d0407ff4f2b5aa4327f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d09bc2c967cf3af24f0f1a1a69564a", "guid": "bfdfe7dc352907fc980b868725387e988c0d30af009d0622f0a4925f959cfda8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ca3a88fec904adfacdac8885b76a434", "guid": "bfdfe7dc352907fc980b868725387e9899c1cd6c727afbe62cd1443798fba7c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f03024ef5007b43f2c2f60c14d68ad9", "guid": "bfdfe7dc352907fc980b868725387e98338ac8ae3dadcfeb3a3fb51f0096a543", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983581eb0e98e45f5430b8466d2d639499", "guid": "bfdfe7dc352907fc980b868725387e983155c1befa51578a07a52a0a25599956", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897e4d556a5d191d0b8c82b792566a7ba", "guid": "bfdfe7dc352907fc980b868725387e98642b54151b51513cd1a2dd64dcafd6c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d122b591bc1afd6cee697b70e179f4d4", "guid": "bfdfe7dc352907fc980b868725387e983ee7e82f2d4c72762ea0d14016d7c368", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cb5158dc59385f33ecd84ec401588f1", "guid": "bfdfe7dc352907fc980b868725387e9801cb5b248c9084579cc8e60eb494b60f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd21418e225c07be9113ee64351d006c", "guid": "bfdfe7dc352907fc980b868725387e981fe991172e4593b488c4b429a0d8225e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98635e31803919265a67ac9f72ff8e68f9", "guid": "bfdfe7dc352907fc980b868725387e98c81e0815d1581a43cc1af0fad5ce8d05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848ccc870912610241109874245be4c62", "guid": "bfdfe7dc352907fc980b868725387e98dc8420dc22bf44a06672387aa5224a98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e680b358194264968ac8e182ebc29c0d", "guid": "bfdfe7dc352907fc980b868725387e9827060b0a1fdef85f04bb484e658b7383", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f37e62fbb19e3de94e19b75d2f6951b1", "guid": "bfdfe7dc352907fc980b868725387e98ed1c158cd93ce9bd5aa118e2cd283b4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cbbf46ba8a5314ba3af021ac9cc91ac", "guid": "bfdfe7dc352907fc980b868725387e98e99c5e414436323606079f3a3fa0ea9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881d947c017683b94708f9eadb2bce382", "guid": "bfdfe7dc352907fc980b868725387e9849f9fb436a0697ecd59328018dd8ae93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc38827d14ec7b7010ba2edc0a150c86", "guid": "bfdfe7dc352907fc980b868725387e98fe4a798a98791898faabe0f22d71259a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98528151aa926f6bf30429d93cc5ac077e", "guid": "bfdfe7dc352907fc980b868725387e98141f268b1584124e62e04e74b1b75e88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98343aebb58b7b119fb44489d999d32c6b", "guid": "bfdfe7dc352907fc980b868725387e98301a0a7e58b10af17aceb2504b3a3971", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d035e31c63f5dbb6158860edce997f", "guid": "bfdfe7dc352907fc980b868725387e9847bc53c4efa9dfda1c7403af4349bc7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853ea3f4c7a222ee4ab06bd7baf6d3986", "guid": "bfdfe7dc352907fc980b868725387e9880b8bc952dd7c79faba8d04e45ea8f13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98306038a98933408b5e0964dfaed5f313", "guid": "bfdfe7dc352907fc980b868725387e98e729c3a805d1847502bb5c73bfecb303", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987391e2b3adfb24d3ba1f0f4de2d9a7ec", "guid": "bfdfe7dc352907fc980b868725387e9808bf92fbfa2674bda950d0e2bcb71dd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad702a933f8247b7b36ce867cd036ced", "guid": "bfdfe7dc352907fc980b868725387e98319c4e20f59cbade17b334db40c15ab7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd27a2daf262f09ca2997c6d2153adee", "guid": "bfdfe7dc352907fc980b868725387e98a13e606cbbfd39a170e763f81b8c82b0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985da9e827eef44a73ab95aff9462753c7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c1f39ecbd39763925ea6fc60055a424", "guid": "bfdfe7dc352907fc980b868725387e9880ce5506b25dd20b711b03704288c587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981106a0f6fcad307025fda310c35dad01", "guid": "bfdfe7dc352907fc980b868725387e98a7557dc539ec4df39cd248dddefe289e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afb34348d4ca0582dab67a050797ce74", "guid": "bfdfe7dc352907fc980b868725387e98bc3c1b20497e20530aa48f475521f59a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053755a13eb6225e9d397c3129ef3df0", "guid": "bfdfe7dc352907fc980b868725387e98c0da8fcff858b56bb9b4aab652636600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98588a59a8da03f243b3fcf33518ae7ab0", "guid": "bfdfe7dc352907fc980b868725387e98b1c54618187d63a60e8ed5483155f597"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d7db13c01b4739ea242604d318d8590", "guid": "bfdfe7dc352907fc980b868725387e98f0097c4a617414a24a43ccaee2061a65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8a85e1120b3e78d0fde9171f1da8e31", "guid": "bfdfe7dc352907fc980b868725387e989a4fbbb040177b251d713e8075c7d4ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859d70b5784b0e07ff4a3550cc24112c1", "guid": "bfdfe7dc352907fc980b868725387e98005d36b45f215eb302ad727713a9c9be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985be21f651a2d10791a71c8a3dde5db4d", "guid": "bfdfe7dc352907fc980b868725387e9823392b89a2aad567b59d013033d15f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801502dfbfa1d21588376f954d8fc96bb", "guid": "bfdfe7dc352907fc980b868725387e981228a83d2b11affd288ecac33aff7d94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989855d09961c05e81f3229919cb05e9ab", "guid": "bfdfe7dc352907fc980b868725387e9854a2591537edaba482cd6403feba19d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846ad8a1648101dce4d30b3eb42c33b9f", "guid": "bfdfe7dc352907fc980b868725387e9885003a044e613a922b7fa982ebd40e7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98667b73b60e10f85c1c8cdcabd9919265", "guid": "bfdfe7dc352907fc980b868725387e9882cfc99a050cfaa4ed27ce805996e941"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98845f13a1912caf0a944923d170df6b24", "guid": "bfdfe7dc352907fc980b868725387e98bee2144c4a5bfefd8cb512b8d5baac86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d8ebe5760afb3be6e31be4d2cdff36", "guid": "bfdfe7dc352907fc980b868725387e98419a9f8a3b82d01236184c3dd702c44c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c6486506c6eb62c73f87edf055be964", "guid": "bfdfe7dc352907fc980b868725387e9899efa4c5119022bafabda491fa07b490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bb9336ffa8b98e167868b2dbab70b52", "guid": "bfdfe7dc352907fc980b868725387e9873ecf4092282782f1466f987e59abdc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98314c560a344cd27587145c28aaed30f6", "guid": "bfdfe7dc352907fc980b868725387e987012ba40086472520aa7e812d3fe9583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840bff16166bbee2c5d402a717b596200", "guid": "bfdfe7dc352907fc980b868725387e986ed95c95e4015c310de490d6af006edd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895def1a69da68e6976effc89272b0c23", "guid": "bfdfe7dc352907fc980b868725387e98bb82c8b4967432d98dc28c4fd639c956"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a706cd16d892239784058d9365fef662", "guid": "bfdfe7dc352907fc980b868725387e98de00129e3270ecf6ff3b9a1449b10c5c"}], "guid": "bfdfe7dc352907fc980b868725387e98ef387155c7be58dd1abcd21a411a4e6e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815a52416b22b7a4a55aab82cf72a39f4", "guid": "bfdfe7dc352907fc980b868725387e98c961a969fb41a7bdab90c538b30528fd"}], "guid": "bfdfe7dc352907fc980b868725387e9817c34e700eb0a17b1be9eca81b57986b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e984da9032ff76790acb4b37e742746ef15", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd53d937e824fad9f4527eeeb684cb40", "name": "Google-Mobile-Ads-SDK"}, {"guid": "bfdfe7dc352907fc980b868725387e9804037656a8578d8e730f9d99c54e40e2", "name": "google_mobile_ads-google_mobile_ads"}, {"guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview"}], "guid": "bfdfe7dc352907fc980b868725387e9811f9347c979613c5502173cd5b43060d", "name": "google_mobile_ads", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b1f16b2926bf8e21151b2cb149aa6540", "name": "google_mobile_ads.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
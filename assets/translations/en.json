{"Busaty - Parents": "Busaty - Parents", "home": "Home", "Religion": "Religion", "NewAddress": "<PERSON><PERSON><PERSON><PERSON>", "type": "Type", "select_absence_date": "Select Absence Date", "current_password": "Current Password", "show": "Show", "login": "<PERSON><PERSON>", "email": "Email", "password": "Password", "forgetPassword": "Forget Password ? ", "remember": "Remember", "notHaveAccount": "Not Have Account ? ", "createAccount": "Create Account", "newPassword": "New Password", "againPassword": "Write Again Password", "changePassword": "Change Password", "createPassword": "Create Password", "signup": "Sign Up", "name": "Name", "phoneNumber": "Phone Number", "confirmPassword": "Confirm Password", "haveAccount": "Have Account ? ", "forget": "Forget Password", "sendCodeRegister": "code will be sent to the registered email to recover the account", "sendCodeAgain": "send code again", "passwordChangeSuccess": "Password Change Success", "goHome": "Go to Home", "oldPassword": "Old Password", "save": "Save", "sendCode": "Send Code", "next": "Next", "add": "Add", "addSon": "Add Son", "code": "Code", "sonData": "Son Data", "showSonOnMap": "Show Son On The Map", "requestAbsence": "Absence", "date": "Date", "requestChangeAddress": "Address Change Requests", "yourAddresses": "Your Addresses", "addManualAddress": "Add a manual address", "addAddressFromMap": "Add Address From Map", "addNewAddress": "Add New Address", "country": "Country", "nameSon": "Name Son", "nameParent": "Name Parent", "cityOrRegin": "City/Region", "streetName": "Street Name", "phase": "Phase", "addAddress": "Add Address", "setting": "Setting", "profile": "Profile", "languages": "Languages", "help": "Help", "english": "English", "arabic": "Arabic", "updateProfile": "Update Profile", "bloodType": "Blood Type", "city": "City", "children": "Children", "stage": "Stage", "absenteeismRequests": "Absenteeism Requests", "searchingAddressOnMap": "Search Address On Map", "logout": "Logout", "validPhone": "please write phone number", "validName": "please write name", "validAddress": "please write address", "validEmail": "please write email", "validPassword": "please write password", "validConfirmPassword": "please write confirmed password", "checkPassword": "Check your Password", "checkEmail": "Email Created Successfully now check your email for verification code", "email_verified": "Your email is verified.", "wrong_code": "wrong code! Please check your email and try again", "sonLocation": "Son Current Location", "address": "Address", "sons": "Sons", "settings": "Settings", "absence_requests": "Absence", "notifications": "Notifications", "getLocations": "Get Locations", "locationDone": "Add Location Successful", "setLocation": "Set Your Location", "morning_trip": "Morning Trip", "evening_trip": "Evening Trip", "full_day": "Full Day", "birth_date": "Birth Date", "grade": "Grade", "grade_edu": "Grade", "school": "School", "add_new_request": "Add New Request", "choose_student": "<PERSON>ose Student", "choose_trip_type": "<PERSON><PERSON>", "location_on_map": "Location On Map", "send_successfully": "Your request sent successfully", "send_failed": "Failed, Make sure filling all data correctly", "send": "Send", "not_found": "Not Found", "is_required": " is required", "bus": "Bus", "pro": "Busaty PRO", "coupon": "Coupon", "enter_coupon": "Enter Coupon Code", "subscribe": "Subscribe", "benefits": "Benefits", "subscribe_with_coupon": "Subscribe With Coupon", "or": "OR", "location": "Location", "delete": "Delete", "good_morning": "Good Morning", "good_evening": "Good Evening", "subscribed_successfully": "You have successfully subscribed", "and": "and", "not_match": "doesn't match", "no_trips": "There is no available trips right now!", "already_subscribed": "You are already subscribed", "yes": "Yes", "no": "No", "search": "Search", "successfully_done": "Successfully Done", "trip_loading": "Loading trip, please wait...", "already_request": "You already have an address change request for this student", "permanent_address_change": "Permanent address change", "temporary_address_change": "Temporary address change", "select_address_change_type": "Select address change type", "date_range": "Date Range", "start_date": "Start Date", "end_date": "End Date", "submit_request": "Submit Request", "temp_address_change_success": "Temporary address change request submitted successfully", "start_date_after_today": "Start date must be after today", "end_date_after_today": "End date must be after today", "end_date_after_start_date": "End date must be after start date", "openTrips": "Open Trips", "tripTypes": "Trip Type", "supervisorName": "Supervisor Name", "busNumber": "Bus Number", "bus_name": "Bus Name", "student": "Student", "all": "All", "unRead": "Un Read", "benefitBusatyBro": "Benefit Busay Bro", "withoutAds": "Without ads", "trackingSonInMoment": "Follow your son in real time while he is on the bus", "tackingBuysInMoment": "Follow the bus's movements while it is on its way to you", "checkInternetConnection": "Check your Internet Connection", "deleteAccountTitle": "Delete Account Confirmation", "deleteAccountConfirm": "Are you sure you want to delete your account permanently?", "deleteAccountNote": "All your data will be deleted from our system automatically after 30 days", "deleteAccount": "Delete Account", "cancel": "Cancel", "contact_us": "Contact Us", "get_in_touch": "Get in Touch", "wed_love_to_hear": "We'd love to hear from you", "enter_your_name": "Enter your name", "enter_your_email": "Enter your email", "describe_problem": "Describe your problem or feedback", "please_enter_name": "Please enter your name", "please_enter_email": "Please enter your email", "please_valid_email": "Please enter a valid email", "please_describe_problem": "Please describe your problem", "message_too_long": "Message cannot be longer than 1000 characters", "contact_directly": "Or contact us directly:", "copy": "Copy", "email_copied": "Email copied to clipboard", "sending": "Sending...", "email_sent": "<PERSON><PERSON> sent successfully!", "failed_to_send": "Failed to send email: {error}", "update_required": "Update Required", "update_message": "A new version of the app is available. Please update to continue using the app.", "update": "Update", "privacy_policy": "Privacy Policy", "play_store": "Play Store", "rate_app": "Rate App", "share_app": "Share App", "more_apps": "More Apps", "wrong_email": "Wrong Email?", "subscribe_to_track": "To track your son on the map, please subscribe", "subscribe_now": "Subscribe now", "trackingDescription": "Get real-time updates and track your son's location instantly with Busaty PRO", "terms_and_conditions": "Terms & Conditions", "loginWithGoogle": "Sign in with Google", "googleSignInError": "<PERSON><PERSON>r signing in with Google", "checkConnection": "Please check your internet connection", "accountDisabled": "This account has been disabled", "loginFailed": "<PERSON><PERSON> failed. Please try again", "completeProfile": "Complete Profile", "uploadPhoto": "Upload Photo", "submit": "Submit", "somethingWentWrong": "Something went wrong", "profileCompleted": "Profile completed successfully", "pleaseCompleteProfile": "Please complete your profile", "pleaseUploadPhoto": "Please upload your photo", "pleaseEnterValidPhone": "Please enter a valid phone number", "pleaseEnterValidName": "Please enter a valid name", "pleaseEnterValidAddress": "Please enter a valid address", "personalInformation": "Personal Information", "subscriptionDetails": "Subscription Details", "planName": "Plan Name", "subscriptionAmount": "Subscription Amount", "startDate": "Start Date", "endDate": "End Date", "paymentMethod": "Payment Method", "subscriptionStatus": "Subscription Status", "active": "Active", "expired": "Expired", "pending": "Pending", "cancelled": "Cancelled", "daysRemaining": "Days Remaining", "subscriptionExpired": "Subscription Expired", "subscriptionActive": "Subscription Active", "maintenance_title": "Under Maintenance", "maintenance_message": "The app is currently being updated. Please try again later.", "maintenance_checking": "Checking...", "maintenance_retry": "Retry", "parents": "Parents", "app_updating": "App Updating", "under_maintenance": "Under Maintenance", "checking": "Checking...", "retry": "Retry", "thank_you_patience": "Thank you for your patience", "add_absence": "Add Absence", "add_child": "Add Child", "image": "Image", "delete_confirmation": "Delete Confirmation", "delete_absence_confirmation": "Are you sure you want to delete this absence request?", "add_child_description": "Enter child information to add to the system"}
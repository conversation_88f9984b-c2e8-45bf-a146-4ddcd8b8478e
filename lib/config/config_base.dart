class ConfigBase {
  static const String fcmUrl = "https://fcm.googleapis.com/fcm/send";
  static const String socketUrl = "https://node.busatyapp.com";

  // static const String base = "https://stage.busatyapp.com/";
  static const String base = "https://test.busatyapp.com/";
  static const String baseUrl = "${base}api/parents/";
  static const String register = "register";
  static const String login = "login";
  static const String firebase = "auth/firebase-login";
  static const String completeProfile = "data/complete";

  static const String forgotPassword = "forgot-password";
  static const String resetPassword = "reset-password";

  static const String profile = "user";
  static const String updateProfile = "data/update";
  static const String changePassword = "password/update";
  static const String createPassword = "new-password";

  static const String logout = "logout";
  static const String children = "childrens/all";
  static const String absence = "absences/index";
  static const String addressChange = "addresses/order/index";
  static const String addChild = "childrens/store";
  static const String deleteChild = "childrens/destroy";
  static const String addAbsence = "absences/store";
  static const String deleteAbsence = "absences/destroy";
  static const String addAddressChange = "addresses/order/store";
  static const String addTemporaryAddressChange = "temporary-addresses";
  static const String getTemporaryAddressRequests = "temporary-addresses";
  static const String cancelTemporaryAddressRequest =
      "temporary-addresses/cancel";
  static const String cancelOrder = "addresses/order/cancel";
  static const String childrenOnMap = "childrens/map/show/";

  // Notifications
  static const String allNotifications = "notifications";
  static const String unreadNotifications = "attendants/messages/new";
  static const String showNotification = "attendants/messages/show";
  static const String storeSchoolNotification =
      "notifications/storeSchoolNotification";
  static const String storeBusNotification =
      "notifications/storeBusNotification";
  static const String schoolFcmTokens = "notifications/schoolFirebaseTokens";
  static const String schoolFcmTokensS = "notifications/schoolFirebaseToken";
  static const String supervisorsFcmTokens =
      "notifications/attendantFirebaseTokens";

  // Subscriptions
  static const String coupon = "subscriptions/coupon";

  // Ads
  static const String ads = "ades";

  // Trips
  static const String tripStatus = "absences/status";

  // Address
  static const String addressStatus = "addresses/order/status";

  // Parent trips current
  static const String parentTripCurrent = "trips";

  // Upload image profile URL
  static const String uploadImageProfile = "childrens/upload/image";

  // Show son by ID
  static const String showSon = "childrens/show";

  // Question helper
  static const String questionHelp = "question";

  // Status URL for user status
  static String get statusUrl => "${base}api/app/status";

  /// App Update Status
  static const String appUpdating = "app/updating";
}

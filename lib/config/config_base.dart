class ConfigBase {
  // static const String fcmUrl = "https://fcm.googleapis.com/fcm/send";
  // static const String socketUrl = "https://node.busatyapp.com";
  static const String socketUrl = "https://node.busatyapp.com/";

  static const String base = "https://stage.busatyapp.com/";
  // static const String base = "https://test.busatyapp.com/";
  static const String baseUrl = "${base}api/attendants/";
  static const String register = "register";
  static const String login = "login";
  static const String signUp = "register";
//rning/parents/messages/store/408
  static const String profile = "user";
  static const String myBus = "my/bus";
  static const String allStudents = "my/students/all";
  static const String myParents = "my/student/parents/all/";
  static const String updateProfile = "data/update";
  static const String changePassword = "general/password/update";
  static const String logout = "logout";
  static const String children = "childrens/all";
  static const String absence = "absences/index";
  static const String addressChange = "addresses/order/index";
  static const String addChildren = "childrens/store/";
  static const String addAbsence = "absences/parents/store";
  static const String addAddressChange = "addresses/order/store";
  static const String childrenOnMap = "childrens/map/show/";
  // Morning Trip
  static const String morningTripStatus = "trips/morning/status";
  static const String startMorningTrip = "trips/morning/start";
  static const String endMorningTrip = "trips/morning/end";
  static const String waitingMorningTrip = "trips/morning/waiting";
  static const String onBusMorningTrip = "trips/morning/onBus";
  static const String absentMorningTrip = "trips/morning/absences";
  static const String presentOnBusMorningTrip = "trips/morning/present_on_bus/";
  static const String removePresentOnBusMorningTrip =
      "trips/morning/removePresent_on_bus";
  static const String studentAbsentMorningTrip = "trips/morning/absent/";
  static const String removeAbsenceMorningTrip =
      "trips/morning/removeAbsenceToSchool";
  static const String sendMessageMorningTrip =
      "trips/morning/parents/messages/store/";
  static const String sendMessageForAllMorningTrip =
      "trips/morning/parents/messages/storeForAll";

  // Evening Trip
  static const String eveningTripStatus = "trips/evening/status";
  static const String startEveningTrip = "trips/evening/start";
  static const String endEveningTrip = "trips/evening/endTrip";
  static const String onBusEveningTrip = "trips/evening/onBus/";
  static const String arrivedEveningTrip = "trips/evening/arrived";
  static const String absentEveningTrip = "trips/evening/absences";
  //https://stage.busatyapp.com/api/attendants/trips/evening/parents/messages/store/70
  static const String arrivedStudentEveningTrip =
      "trips/evening/arrivedStudent/";
  static const String removeArrivedHomeEveningTrip =
      "trips/evening/removeArrivedStudent/";
  static const String studentAbsentEveningTrip = "trips/evening/absent/";
  static const String removeAbsenceEveningTrip =
      "trips/evening/removeAbsenceToHome/";
  static const String sendMessageEveningTrip =
      "trips/evening/parents/messages/store/";
  static const String sendMessageForAllEveningTrip =
      "trips/evening/parents/messages/storeForAll";

  // Notifications
  static const String allParentsFcmTokens =
      "notifications/allParentFirebaseTokens";
  static const String parentFcmTokens = "notifications/parentFirebaseTokens";
  static const String showNotifications =
      "notifications/showAttendantNotification";

  // Ads
  static const String ads = "ades";

  /// current trip morning url
  static const String currentTripMorning = "trips/morning/status";

  /// current trip evening url
  static const String currentTripEvening = "trips/evening/status";

  /// question helper
  static const String questionHelp = "question";

  /// App Update Status
  static const String appUpdating = "app/updating";
}

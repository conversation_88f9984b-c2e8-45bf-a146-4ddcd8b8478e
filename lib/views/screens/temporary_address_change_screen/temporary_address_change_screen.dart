import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';
import 'package:busaty_parents/bloc/cubit/children_cubit/children_cubit.dart';
import 'package:busaty_parents/bloc/cubit/temporary_address_change_cubit/temporary_address_change_cubit.dart';
import 'package:busaty_parents/bloc/cubit/temporary_address_change_cubit/temporary_address_change_states.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/constant/path_route_name.dart';
import 'package:busaty_parents/data/models/child_model.dart';
import 'package:busaty_parents/data/models/pickup_location_local_models/pickup_location_local_models.dart';
import 'package:busaty_parents/helper/convert_arabic_to_regular_numerals.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/utils/assets_utils.dart';
import 'package:busaty_parents/views/custom_widgets/custom_button.dart';
import 'package:busaty_parents/views/custom_widgets/custom_form_field_border.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/widgets/custom_appbar.dart';
import 'package:busaty_parents/widgets/pick_location_widget.dart';

class TemporaryAddressChangeScreen extends StatefulWidget {
  static const String routeName = PathRouteName.temporaryAddressChangeScreen;
  const TemporaryAddressChangeScreen({super.key});
  @override
  State<TemporaryAddressChangeScreen> createState() =>
      _TemporaryAddressChangeScreenState();
}

class _TemporaryAddressChangeScreenState
    extends State<TemporaryAddressChangeScreen> {
  String? selectedStudentId;
  String? schoolId;
  String? childName;
  String? newAddress;
  String? newAddressPickup;
  LatLong? position;
  List<ChildModel>? children;
  PickupLocationLocalModels? pickupLocationLocalModels;

  // Date range selection
  DateTime? startDate;
  DateTime? endDate;

  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.temporaryAddressChange.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30.r),
            topRight: Radius.circular(30.r),
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                20.verticalSpace,
                // Header section with icon
                Center(
                  child: Container(
                    width: 70.w,
                    height: 70.w,
                    decoration: const BoxDecoration(
                      color: Color(0xFFE6F2FF),
                      shape: BoxShape.circle,
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.timer,
                        color: TColor.mainColor,
                        size: 35,
                      ),
                    ),
                  ),
                ),
                15.verticalSpace,
                Center(
                  child: CustomText(
                    text: AppStrings.temporaryAddressChange.tr(),
                    fontSize: 20,
                    fontW: FontWeight.bold,
                    color: TColor.mainColor,
                  ),
                ),
                20.verticalSpace,
                CustomText(
                  text: AppStrings.chooseStudent.tr(),
                  fontSize: 16,
                  fontW: FontWeight.bold,
                ),
                10.verticalSpace,
                _buildStudentSelector(),
                20.verticalSpace,
                CustomText(
                  text: AppStrings.newAddress.tr(),
                  fontSize: 16,
                  fontW: FontWeight.bold,
                ),
                10.verticalSpace,
                _buildAddressField(),
                20.verticalSpace,
                CustomText(
                  text: AppStrings.locationOnMap.tr(),
                  fontSize: 16,
                  fontW: FontWeight.bold,
                ),
                10.verticalSpace,
                _buildLocationSelector(),
                20.verticalSpace,
                CustomText(
                  text: AppStrings.dateRange.tr(),
                  fontSize: 16,
                  fontW: FontWeight.bold,
                ),
                10.verticalSpace,
                _buildDateRangeSelector(),
                30.verticalSpace,
                _buildSubmitButton(),
                30.verticalSpace,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStudentSelector() {
    return BlocBuilder<ChildrenCubit, ChildrenState>(
      builder: (context, state) {
        if (state is ChildrenFailedState) {
          return Center(child: Text(AppStrings.notFound.tr()));
        } else if (state is ChildrenInitialState) {
          BlocProvider.of<ChildrenCubit>(context).getStudent();
          return const Center(
            child: CircularProgressIndicator(
              color: TColor.mainColor,
            ),
          );
        } else if (state is ChildrenSuccessState) {
          children = state.childrens;
          List<ChildModel> list = [];
          if (children != null) {
            list.addAll(children!);
          }
          return Container(
            height: 53.h,
            decoration: BoxDecoration(
              color: TColor.fillFormFieldB,
              borderRadius: BorderRadius.circular(15.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Center(
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  isExpanded: true,
                  hint: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Text(AppStrings.chooseStudent.tr()),
                  ),
                  value: selectedStudentId,
                  onChanged: (String? newValue) {
                    setState(() {
                      selectedStudentId = newValue!;
                      for (var element in list) {
                        if (element.id.toString() == newValue) {
                          schoolId = element.schoolId.toString();
                          childName = element.name;
                        }
                      }
                    });
                  },
                  items: list.map<DropdownMenuItem<String>>((ChildModel value) {
                    return DropdownMenuItem<String>(
                      value: value.id.toString(),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 20.w),
                        child: Text(value.name!),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          );
        } else {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
      },
    );
  }

  Widget _buildAddressField() {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: CustomFormFieldWithBorder(
        onChanged: (p0) {
          newAddress = p0;
        },
        heightA: 53,
        hintText: AppStrings.newAddress.tr(),
        borderColor: TColor.fillFormFieldB,
        fillColor: TColor.fillFormFieldB,
        radiusNumber: 15.0,
        paddingRight: 20.w,
        paddingLeft: 20.w,
        contentPaddingVertical: 15,
        contentPaddingHorizontal: 15,
      ),
    );
  }

  Widget _buildLocationSelector() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: InkWell(
        onTap: () async {
          pickupLocationLocalModels =
              await Navigator.push(context, MaterialPageRoute(builder: (ctx) {
            return const PickLocationWidget();
          }));
          setState(() {});
          position = LatLong(pickupLocationLocalModels!.lat!,
              pickupLocationLocalModels!.long!);
          newAddressPickup = pickupLocationLocalModels!.address;
        },
        child: Container(
          height: 53.h,
          decoration: BoxDecoration(
            color: TColor.fillFormFieldB,
            borderRadius: BorderRadius.circular(15.0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.location_on,
                  color: TColor.mainColor,
                ),
                10.horizontalSpace,
                Expanded(
                  child: CustomText(
                    text: pickupLocationLocalModels?.address ??
                        AppStrings.locationOnMap.tr(),
                    fontSize: 16,
                    maxLine: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDateRangeSelector() {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: startDate ?? DateTime.now(),
                firstDate: DateTime.now().add(const Duration(days: 0)),
                lastDate: DateTime(2101),
              );
              if (picked != null && picked != startDate) {
                setState(() {
                  startDate = picked;
                });
              }
            },
            child: Container(
              height: 53.h,
              decoration: BoxDecoration(
                color: TColor.fillFormFieldB,
                borderRadius: BorderRadius.circular(15.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        text: startDate == null
                            ? AppStrings.startDate.tr()
                            : DateFormat('yyyy-MM-dd').format(startDate!),
                        fontSize: 14,
                      ),
                      const Icon(
                        Icons.calendar_today,
                        color: TColor.mainColor,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        10.horizontalSpace,
        Expanded(
          child: InkWell(
            onTap: () async {
              final DateTime? picked = await showDatePicker(
                context: context,
                initialDate: endDate ?? (startDate ?? DateTime.now().add(const Duration(days: 2))),
                firstDate: startDate ?? DateTime.now().add(const Duration(days: 1)),
                lastDate: DateTime(2101),
              );
              if (picked != null && picked != endDate) {
                setState(() {
                  endDate = picked;
                });
              }
            },
            child: Container(
              height: 53.h,
              decoration: BoxDecoration(
                color: TColor.fillFormFieldB,
                borderRadius: BorderRadius.circular(15.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        text: endDate == null
                            ? AppStrings.endDate.tr()
                            : DateFormat('yyyy-MM-dd').format(endDate!),
                        fontSize: 14,
                      ),
                      const Icon(
                        Icons.calendar_today,
                        color: TColor.mainColor,
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return BlocConsumer<TemporaryAddressChangeCubit, TemporaryAddressChangeState>(
      listener: (context, state) {
        if (state is TemporaryAddressChangeSuccessState) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              backgroundColor: TColor.greenSuccess,
              content: CustomText(
                text: AppStrings.tempAddressChangeSuccess.tr(),
                fontSize: 16,
                maxLine: 2,
                color: TColor.white,
              ),
            ),
          );
          Navigator.pop(context);
        } else if (state is TemporaryAddressChangeErrorState) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              backgroundColor: TColor.redAccent,
              content: CustomText(
                text: state.error,
                fontSize: 16,
                maxLine: 2,
                color: TColor.white,
              ),
            ),
          );
        }
      },
      builder: (context, state) {
        return CustomButton(
          bgColor: TColor.mainColor,
          text: AppStrings.submitRequest.tr(),
          onTap: _validateAndSubmit,
          loading: state is TemporaryAddressChangeLoadingState,
        );
      },
    );
  }

  bool _validateForm() {
    if (selectedStudentId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "${AppStrings.chooseStudent.tr()} ${AppStrings.isRequired.tr()}",
            fontSize: 16,
            maxLine: 2,
            color: TColor.white,
          ),
        ),
      );
      return false;
    }

    if ((newAddress == null || newAddress!.isEmpty) &&
        (newAddressPickup == null || newAddressPickup!.isEmpty)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "${AppStrings.newAddress.tr()} ${AppStrings.isRequired.tr()}",
            fontSize: 16,
            maxLine: 2,
            color: TColor.white,
          ),
        ),
      );
      return false;
    }

    if (position == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "${AppStrings.locationOnMap.tr()} ${AppStrings.isRequired.tr()}",
            fontSize: 16,
            maxLine: 2,
            color: TColor.white,
          ),
        ),
      );
      return false;
    }

    if (startDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "${AppStrings.startDate.tr()} ${AppStrings.isRequired.tr()}",
            fontSize: 16,
            maxLine: 2,
            color: TColor.white,
          ),
        ),
      );
      return false;
    }

    if (endDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: "${AppStrings.endDate.tr()} ${AppStrings.isRequired.tr()}",
            fontSize: 16,
            maxLine: 2,
            color: TColor.white,
          ),
        ),
      );
      return false;
    }

    // التحقق من أن تاريخ البدء بعد اليوم الحالي
    final DateTime today = DateTime(
      DateTime.now().year,
      DateTime.now().month,
      DateTime.now().day,
    );

    // if (!startDate!.isAfter(today)) {
    //   ScaffoldMessenger.of(context).showSnackBar(
    //     SnackBar(
    //       backgroundColor: TColor.redAccent,
    //       content: CustomText(
    //         text: AppStrings.startDateAfterToday.tr(),
    //         fontSize: 16,
    //         maxLine: 2,
    //         color: TColor.white,
    //       ),
    //     ),
    //   );
    //   return false;
    // }

    // // التحقق من أن تاريخ الانتهاء بعد اليوم الحالي
    // if (!endDate!.isAfter(today)) {
    //   ScaffoldMessenger.of(context).showSnackBar(
    //     SnackBar(
    //       backgroundColor: TColor.redAccent,
    //       content: CustomText(
    //         text: AppStrings.endDateAfterToday.tr(),
    //         fontSize: 16,
    //         maxLine: 2,
    //         color: TColor.white,
    //       ),
    //     ),
    //   );
    //   return false;
    // }

    // التحقق من أن تاريخ الانتهاء بعد تاريخ البدء
    if (!endDate!.isAfter(startDate!)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          backgroundColor: TColor.redAccent,
          content: CustomText(
            text: AppStrings.endDateAfterStartDate.tr(),
            fontSize: 16,
            maxLine: 2,
            color: TColor.white,
          ),
        ),
      );
      return false;
    }

    return true;
  }

  void _validateAndSubmit() {
    if (!_validateForm()) return;

    final String address = newAddress ?? newAddressPickup ?? "";
    final String fromDate = convertArabicToRegularNumerals(DateFormat('yyyy-MM-dd').format(startDate!));
    final String toDate = convertArabicToRegularNumerals(DateFormat('yyyy-MM-dd').format(endDate!));

    BlocProvider.of<TemporaryAddressChangeCubit>(context).addTemporaryAddressChange(
      studentId: selectedStudentId!,
      address: address,
      latitude: position!.latitude.toString(),
      longitude: position!.longitude.toString(),
      fromDate: fromDate,
      toDate: toDate,
    );
  }
}

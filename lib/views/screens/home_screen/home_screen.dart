import 'package:busaty_parents/bloc/cubit/register_cubit/register_cubit.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:get_it/get_it.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import 'package:provider/provider.dart';
import 'package:busaty_parents/bloc/cubit/ads_cubit/ads_cubit.dart';
import 'package:busaty_parents/bloc/cubit/check_internet_cubit/check_internet_cubit.dart';
import 'package:busaty_parents/bloc/cubit/current_trip_cubit/current_trip_cubit.dart';
import 'package:busaty_parents/bloc/cubit/current_trip_cubit/current_trip_states.dart';
import 'package:busaty_parents/bloc/cubit/notifications_cubit/notifications_cubit.dart';
import 'package:busaty_parents/config/config_base.dart';
import 'package:busaty_parents/config/global_variable.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/constant/path_route_name.dart';
import 'package:busaty_parents/data/repo/user_repo.dart';
import 'package:busaty_parents/helper/app_link_deep_link.dart';
import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:busaty_parents/helper/network_serviecs.dart';
import 'package:busaty_parents/services/ad_mob_service.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/views/screens/login_screen/login_screen.dart';
import 'package:busaty_parents/views/screens/notifications_screen/notifications_screen.dart';
import 'package:busaty_parents/widgets/home_widgets/custom_gird_w.dart';
import 'package:busaty_parents/widgets/home_widgets/custom_name_w.dart';

import '../../../services/payment_service.dart';
import '../../../widgets/carousel_widget.dart';

class HomeScreen extends StatefulWidget {
  static const String routeName = PathRouteName.home;

  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final _userRepo = UserRepo();
  bool isUserDataLoaded = false;
  bool hasError = false;

  // Ad Related
  late AdMobService _adMobService;
  BannerAd? _banner;

  @override
  void initState() {
    super.initState();
    _initializeHomeScreen();
  }

  Future<void> _initializeHomeScreen() async {
    if (!mounted) return;

    try {
      initializeFCMToken();
      await fetchUserStatus();
      checkAppVersion();
      deeblink();

      if (!mounted) return;

      debugPrint('token: $token');
      debugPrint('tempToken: $tempToken');
      AdsCubit.get(context).getAds();
      context.read<CheckInternetCubit>().checkConnectivity();

      final value = await _userRepo.repoUser();
      if (!mounted) return;
      haveSchoolCheck(checkError: value, context: context);
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      debugPrint(e.toString());
    }
  }

  Future<void> fetchUserStatus() async {
    if (!mounted) return;

    String defaultName = "parents";
    await context.read<RegisterCubit>().getUserStatus(defaultName);
    debugPrint(
        "User Status: ${context.read<RegisterCubit>().version.toString()}");

    if (mounted) {
      setState(() {});
    }
  }

  void checkAppVersion() {
    // Get current version from Info.plist/build.gradle
    final currentVersion = "42"; // Current app version
    final requiredVersion = context.read<RegisterCubit>().version;

    debugPrint("requiredVersion $requiredVersion");
    debugPrint("currentVersion $currentVersion");

    if (requiredVersion != null) {
      final int r = int.tryParse(requiredVersion)!;
      final int c = int.tryParse(currentVersion)!;
      final needsUpdate = c < r;
      debugPrint("needsUpdate $needsUpdate");
      if (needsUpdate) {
        Future.microtask(() => showForceUpdateDialog(
            context,
            Theme.of(context).platform == TargetPlatform.iOS
                ? "https://apps.apple.com/eg/app/busaty-school/id6740827987?l=ar"
                : "https://play.google.com/store/apps/details?id=com.busaty.school"));
      }
    }
  }

  void deeblink() async {
    await AppLinksDeepLink.instance.initDeepLinks();
  }

  @override
  void didChangeDependencies() async {
    super.didChangeDependencies();
    await _initializeAds();
  }

  Future<void> _initializeAds() async {
    if (!mounted) return;

    await getUserData();

    if (!mounted) return;

    if (PaymentService.instance.isProUser == false &&
        subscriptionStatus == false) {
      _adMobService = GetIt.instance.get<AdMobService>();
      await _adMobService.initialization;

      if (!mounted) return;

      setState(() {
        _banner = BannerAd(
          adUnitId: _adMobService.bannerAdUnitId!,
          size: AdSize.fullBanner,
          request: const AdRequest(),
          listener: _adMobService.bannerListener,
        )..load();
      });
    } else {
      if (mounted) {
        setState(() {
          _banner = null;
        });
      }
    }
  }

  Future<void> getUserData() async {
    if (!mounted) return;

    try {
      var response =
          await NetworkService().get(url: ConfigBase.profile, isAuth: true);
      debugPrint('${response.data}');
      userEmail = response.data["email"];
      userName = response.data["name"];
      userImageUrl = response.data["logo_path"];
      userPhone = response.data["phone"];
      userAddress = response.data["address"];
      subscriptionStatus = response.data["subscription_status"] ?? false;

      if (mounted) {
        setState(() {
          isUserDataLoaded = true;
        });
      }
    } catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());

      if (mounted) {
        setState(() {
          isUserDataLoaded = true;
          hasError = true;
        });
      }
    }
  }

  void haveSchoolCheck({String? checkError, BuildContext? context}) {
    if (checkError != "OK" &&
        Provider.of<CheckInternetCubit>(context!, listen: false).disConnected ==
            false) {
      CacheHelper.remove("token");
      token = null;
      Navigator.pushReplacementNamed(context, LoginScreen.routeName);
    }
  }

  /// Call when user close the app
  @override
  void dispose() {
    PaymentService.instance.dispose();
    _banner?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print(context.locale.toString());
    return Scaffold(
      backgroundColor: Colors.grey.withValues(alpha: 0.05),
      body: StreamBuilder<Object>(
          stream: Connectivity().onConnectivityChanged,
          builder: (context, snapshot) {
            context
                .read<CheckInternetCubit>()
                .checkConnectionStream(data: snapshot, context: context);
            return RefreshIndicator(
              onRefresh: () async {
                AdsCubit.get(context).getAds();
                context.read<CurrentTripCubit>().getCurrentTrip();
                fetchUserStatus();
              },
              child: SingleChildScrollView(
                child: SafeArea(
                  child: context.watch<CheckInternetCubit>().disConnected
                      ? Stack(
                          children: [
                            Container(
                              width: 1.sw,
                              height: 1.sh,
                              decoration: const BoxDecoration(
                                image: DecorationImage(
                                    image: AssetImage("assets/images/bg.png"),
                                    fit: BoxFit.cover),
                              ),
                              child: const CustomNameW(),
                            ),
                            Positioned(
                              top: 65.w,
                              child: Container(
                                width: 1.sw,
                                height: 1.sh,
                                decoration: BoxDecoration(
                                  color: TColor.white,
                                  borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(30.r),
                                    topLeft: Radius.circular(30.r),
                                  ),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Image.asset(
                                        'assets/images/disconnect_internet.png'),
                                    CustomText(
                                      text: AppStrings.checkInternetConnection
                                          .tr(),
                                      fontSize: 25,
                                      fontW: FontWeight.w600,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        )
                      : Stack(
                          children: [
                            Container(
                              width: 1.sw,
                              height: 1.sh,
                              decoration: const BoxDecoration(
                                image: DecorationImage(
                                    image: AssetImage("assets/images/bg.png"),
                                    fit: BoxFit.cover),
                              ),
                              child: isUserDataLoaded
                                  ? const CustomNameW()
                                  : const Center(
                                      child: CircularProgressIndicator(
                                      color: TColor.white,
                                    )),
                            ),
                            isUserDataLoaded
                                ? Positioned(
                                    top: 65.w,
                                    child: Container(
                                      width: 1.sw,
                                      height: 1.sh,
                                      decoration: BoxDecoration(
                                        color: TColor.white,
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(30.r),
                                          topLeft: Radius.circular(30.r),
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          20.verticalSpace,

                                          // Welcome Card
                                          Container(
                                            margin: EdgeInsets.symmetric(
                                                horizontal: 20.w),
                                            padding: EdgeInsets.all(16.w),
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                colors: [
                                                  TColor.mainColor
                                                      .withValues(alpha: 0.1),
                                                  TColor.mainColor
                                                      .withValues(alpha: 0.05),
                                                ],
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(20.r),
                                              border: Border.all(
                                                color: TColor.mainColor
                                                    .withValues(alpha: 0.2),
                                                width: 1,
                                              ),
                                            ),
                                            child: Row(
                                              children: [
                                                Container(
                                                  width: 45.w,
                                                  height: 45.w,
                                                  decoration: BoxDecoration(
                                                    color: TColor.mainColor
                                                        .withValues(alpha: 0.2),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.waving_hand_rounded,
                                                    color: TColor.mainColor,
                                                    size: 24.sp,
                                                  ),
                                                ),
                                                SizedBox(width: 12.w),
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      CustomText(
                                                        text: 'welcome'.tr(),
                                                        fontSize: 16,
                                                        fontW: FontWeight.w600,
                                                        color: TColor.text,
                                                      ),
                                                      SizedBox(height: 2.h),
                                                      CustomText(
                                                        text: userName ??
                                                            'parent'.tr(),
                                                        fontSize: 14,
                                                        fontW: FontWeight.w500,
                                                        color: TColor.mainColor,
                                                      ),
                                                      SizedBox(height: 2.h),
                                                      CustomText(
                                                        text: 'have_nice_day'
                                                            .tr(),
                                                        fontSize: 12,
                                                        color: TColor.tabColors,
                                                      ),
                                                    ],
                                                  ),
                                                ),

                                                // Notification Button
                                                InkWell(
                                                  onTap: () {
                                                    NotificationsCubit.get(
                                                            context)
                                                        .getAllNotifications(
                                                            page: 1,
                                                            isFirst: true);
                                                    Navigator.pushNamed(
                                                        context,
                                                        NotificationsScreen
                                                            .routeName);
                                                  },
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          20.r),
                                                  child: Container(
                                                    width: 40.w,
                                                    height: 40.w,
                                                    decoration: BoxDecoration(
                                                      color: TColor.mainColor,
                                                      shape: BoxShape.circle,
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: TColor
                                                              .mainColor
                                                              .withValues(
                                                                  alpha: 0.3),
                                                          spreadRadius: 1,
                                                          blurRadius: 4,
                                                          offset: const Offset(
                                                              0, 2),
                                                        ),
                                                      ],
                                                    ),
                                                    child: Icon(
                                                      Icons
                                                          .notifications_rounded,
                                                      color: Colors.white,
                                                      size: 20.sp,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),

                                          20.verticalSpace,

                                          // Ads Section Header
                                          Container(
                                            margin: EdgeInsets.symmetric(
                                                horizontal: 20.w),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.campaign_rounded,
                                                  color: TColor.mainColor,
                                                  size: 24.sp,
                                                ),
                                                SizedBox(width: 8.w),
                                                CustomText(
                                                  text: 'offers_ads'.tr(),
                                                  fontSize: 18,
                                                  fontW: FontWeight.w600,
                                                  color: TColor.text,
                                                ),
                                              ],
                                            ),
                                          ),

                                          20.verticalSpace,
                                          BlocBuilder<AdsCubit, AdsState>(
                                            builder: (context, state) {
                                              final ads = AdsCubit.get(context)
                                                  .ads
                                                  ?.data;
                                              return CarouselWidget(
                                                  items: List<Widget>.generate(
                                                ads?.length ?? 0,
                                                (index) => InkWell(
                                                  onTap: () async {
                                                    final Uri url = Uri.parse(
                                                        ads?[index].link ?? '');
                                                    if (!await launchUrl(url)) {
                                                      throw Exception(
                                                          'Could not launch $url');
                                                    }
                                                  },
                                                  child: Container(
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 8.w),
                                                    decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              20.r),
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: TColor
                                                              .mainColor
                                                              .withValues(
                                                                  alpha: 0.2),
                                                          spreadRadius: 1,
                                                          blurRadius: 12,
                                                          offset: const Offset(
                                                              0, 6),
                                                        ),
                                                      ],
                                                    ),
                                                    child: ClipRRect(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              20.r),
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          // Image with overlay
                                                          Stack(
                                                            children: [
                                                              Container(
                                                                width: double
                                                                    .infinity,
                                                                height: 150.h,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  image:
                                                                      DecorationImage(
                                                                    image: NetworkImage(
                                                                        ads?[index].imagePath ??
                                                                            ''),
                                                                    fit: BoxFit
                                                                        .cover,
                                                                  ),
                                                                ),
                                                              ),

                                                              // Gradient overlay
                                                              Container(
                                                                width: double
                                                                    .infinity,
                                                                height: 150.h,
                                                                decoration:
                                                                    BoxDecoration(
                                                                  gradient:
                                                                      LinearGradient(
                                                                    begin: Alignment
                                                                        .topCenter,
                                                                    end: Alignment
                                                                        .bottomCenter,
                                                                    colors: [
                                                                      Colors
                                                                          .transparent,
                                                                      Colors
                                                                          .black
                                                                          .withValues(
                                                                              alpha: 0.3),
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),

                                                              // Play icon overlay
                                                              Positioned(
                                                                top: 12.h,
                                                                right: 12.w,
                                                                child:
                                                                    Container(
                                                                  width: 35.w,
                                                                  height: 35.w,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Colors
                                                                        .white
                                                                        .withValues(
                                                                            alpha:
                                                                                0.9),
                                                                    shape: BoxShape
                                                                        .circle,
                                                                  ),
                                                                  child: Icon(
                                                                    Icons
                                                                        .open_in_new_rounded,
                                                                    color: TColor
                                                                        .mainColor,
                                                                    size: 18.sp,
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),

                                                          // Title section
                                                          Container(
                                                            width:
                                                                double.infinity,
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    vertical:
                                                                        16.h,
                                                                    horizontal:
                                                                        20.w),
                                                            decoration:
                                                                BoxDecoration(
                                                              gradient: LinearGradient(
                                                                  colors: [
                                                                    TColor
                                                                        .mainColor,
                                                                    TColor
                                                                        .mainColor
                                                                        .withValues(
                                                                            alpha:
                                                                                0.9)
                                                                  ],
                                                                  begin: Alignment
                                                                      .centerLeft,
                                                                  end: Alignment
                                                                      .centerRight),
                                                            ),
                                                            child: Row(
                                                              children: [
                                                                Expanded(
                                                                  child: Text(
                                                                    ads?[index]
                                                                            .title ??
                                                                        '',
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          16.sp,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w600,
                                                                      color: Colors
                                                                          .white,
                                                                    ),
                                                                    maxLines: 2,
                                                                    overflow:
                                                                        TextOverflow
                                                                            .ellipsis,
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                    width: 8.w),
                                                                Icon(
                                                                  Icons
                                                                      .arrow_forward_ios,
                                                                  color: Colors
                                                                      .white,
                                                                  size: 16.sp,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ));
                                            },
                                          ),
                                          25.verticalSpace,

                                          // Quick Services Section Header
                                          Container(
                                            margin: EdgeInsets.symmetric(
                                                horizontal: 20.w),
                                            child: Row(
                                              children: [
                                                Icon(
                                                  Icons.dashboard_rounded,
                                                  color: TColor.mainColor,
                                                  size: 24.sp,
                                                ),
                                                SizedBox(width: 8.w),
                                                CustomText(
                                                  text: 'quick_services'.tr(),
                                                  fontSize: 18,
                                                  fontW: FontWeight.w600,
                                                  color: TColor.text,
                                                ),
                                              ],
                                            ),
                                          ),

                                          15.verticalSpace,
                                          const CustomGirdW(),
                                          20.verticalSpace,
                                          BlocBuilder<CurrentTripCubit,
                                              CurrentTripStates>(
                                            builder: (context, states) {
                                              if (states
                                                  is CurrentTripLoadingStates) {
                                                return const SizedBox();
                                              } else if (states
                                                  is CurrentTripSuccessStates) {
                                                if (states.currentTripModels!
                                                    .data!.isEmpty) {
                                                  return const SizedBox();
                                                } else {
                                                  return Column(
                                                    children: [
                                                      // Current Trips Section Header
                                                      Container(
                                                        margin: EdgeInsets
                                                            .symmetric(
                                                                horizontal:
                                                                    20.w),
                                                        child: Row(
                                                          children: [
                                                            Icon(
                                                              Icons
                                                                  .directions_bus_filled_rounded,
                                                              color: TColor
                                                                  .mainColor,
                                                              size: 24.sp,
                                                            ),
                                                            SizedBox(
                                                                width: 8.w),
                                                            CustomText(
                                                              text:
                                                                  'current_trips'
                                                                      .tr(),
                                                              fontSize: 18,
                                                              fontW: FontWeight
                                                                  .w600,
                                                              color:
                                                                  TColor.text,
                                                            ),
                                                          ],
                                                        ),
                                                      ),

                                                      20.verticalSpace,
                                                      Container(
                                                        margin: EdgeInsets
                                                            .symmetric(
                                                                horizontal:
                                                                    16.w),
                                                        child: InkWell(
                                                          onTap: () {
                                                            context
                                                                .read<
                                                                    CurrentTripCubit>()
                                                                .getCurrentTrip();
                                                            Navigator.pushNamed(
                                                                context,
                                                                PathRouteName
                                                                    .openTripScreen);
                                                          },
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(
                                                                      16.r),
                                                          child: Container(
                                                            width:
                                                                double.infinity,
                                                            padding:
                                                                EdgeInsets.all(
                                                                    20.w),
                                                            decoration:
                                                                BoxDecoration(
                                                              gradient:
                                                                  LinearGradient(
                                                                colors: [
                                                                  TColor
                                                                      .mainColor,
                                                                  TColor
                                                                      .mainColor
                                                                      .withValues(
                                                                          alpha:
                                                                              0.8),
                                                                ],
                                                                begin: Alignment
                                                                    .centerLeft,
                                                                end: Alignment
                                                                    .centerRight,
                                                              ),
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          16.r),
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  color: TColor
                                                                      .mainColor
                                                                      .withValues(
                                                                          alpha:
                                                                              0.3),
                                                                  spreadRadius:
                                                                      1,
                                                                  blurRadius: 8,
                                                                  offset:
                                                                      const Offset(
                                                                          0, 4),
                                                                ),
                                                              ],
                                                            ),
                                                            child: Row(
                                                              children: [
                                                                Container(
                                                                  width: 50.w,
                                                                  height: 50.w,
                                                                  decoration:
                                                                      BoxDecoration(
                                                                    color: Colors
                                                                        .white
                                                                        .withValues(
                                                                            alpha:
                                                                                0.2),
                                                                    shape: BoxShape
                                                                        .circle,
                                                                  ),
                                                                  child: Icon(
                                                                    Icons
                                                                        .directions_bus_rounded,
                                                                    color: Colors
                                                                        .white,
                                                                    size: 24.sp,
                                                                  ),
                                                                ),
                                                                SizedBox(
                                                                    width:
                                                                        16.w),
                                                                Expanded(
                                                                  child: Column(
                                                                    crossAxisAlignment:
                                                                        CrossAxisAlignment
                                                                            .start,
                                                                    children: [
                                                                      CustomText(
                                                                        text: AppStrings
                                                                            .openTrips
                                                                            .tr(),
                                                                        fontSize:
                                                                            18,
                                                                        fontW: FontWeight
                                                                            .w600,
                                                                        color: Colors
                                                                            .white,
                                                                      ),
                                                                      SizedBox(
                                                                          height:
                                                                              4.h),
                                                                      CustomText(
                                                                        text: 'click_to_view_trips'
                                                                            .tr(),
                                                                        fontSize:
                                                                            14,
                                                                        color: Colors
                                                                            .white,
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ),
                                                                Icon(
                                                                  Icons
                                                                      .arrow_forward_ios,
                                                                  color: Colors
                                                                      .white,
                                                                  size: 20.sp,
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                      )
                                                    ],
                                                  );
                                                }
                                              } else if (states
                                                  is CurrentTripErrorStates) {
                                                return const SizedBox();
                                              } else {
                                                return const SizedBox();
                                              }
                                            },
                                          ),
                                          const Spacer(),
                                          _banner == null
                                              ? const SizedBox()
                                              : Container(
                                                  margin: EdgeInsets.symmetric(
                                                      horizontal: 16.w),
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12.r),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.grey
                                                            .withValues(
                                                                alpha: 0.1),
                                                        spreadRadius: 1,
                                                        blurRadius: 5,
                                                        offset:
                                                            const Offset(0, 2),
                                                      ),
                                                    ],
                                                  ),
                                                  child: ClipRRect(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12.r),
                                                    child: SizedBox(
                                                      height: 60,
                                                      child: AdWidget(
                                                          ad: _banner!),
                                                    ),
                                                  ),
                                                ),
                                          30.verticalSpace,
                                        ],
                                      ),
                                    ),
                                  )
                                : const SizedBox(),
                          ],
                        ),
                ),
              ),
            );
          }),
    );
  }
}

Future<void> showForceUpdateDialog(BuildContext context, String appUrl) async {
  // Add a small delay to ensure the widget tree is built
  await Future.delayed(const Duration(milliseconds: 100));

  if (!context.mounted) return;

  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        contentPadding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16),
        insetPadding:
            const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
        alignment: Alignment.center,
        backgroundColor: Colors.white,
        title: Container(
          width: 72.w,
          height: 72.h,
          decoration: const BoxDecoration(
              color: Color(0xffFDEEEE), shape: BoxShape.circle),
          child: const Center(
            child: Icon(
              Icons.system_update,
              color: TColor.mainColor,
              size: 35,
            ),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              AppStrings.updateRequired.tr(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              AppStrings.updateMessage.tr(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () async {
                final Uri url = Uri.parse(appUrl);
                if (await canLaunchUrl(url)) {
                  await launchUrl(url);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: TColor.mainColor,
                minimumSize: Size(double.infinity, 45.h),
              ),
              child: Text(
                AppStrings.update.tr(),
                style: TextStyle(
                  color: TColor.white,
                  fontSize: 18.sp,
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}

import 'dart:io';

import 'package:flutter/material.dart';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:image_picker/image_picker.dart';

import 'package:logger/logger.dart';
import 'package:open_street_map_search_and_pick/open_street_map_search_and_pick.dart';
import 'package:busaty_parents/bloc/cubit/add_son_cubit/add_son_cubit.dart';
import 'package:busaty_parents/bloc/cubit/add_son_cubit/add_son_states.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/constant/path_route_name.dart';
import 'package:busaty_parents/data/models/pickup_location_local_models/pickup_location_local_models.dart';
import 'package:busaty_parents/translations/local_keys.g.dart';
import 'package:busaty_parents/utils/assets_utils.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_button.dart';
import 'package:busaty_parents/views/custom_widgets/custom_form_field_border.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';
import 'package:busaty_parents/widgets/custom_appbar.dart';
import 'package:busaty_parents/widgets/pick_location_widget.dart';

import '../../../bloc/cubit/children_cubit/children_cubit.dart';

class AddSonScreen extends StatefulWidget {
  static const String routeName = PathRouteName.addSon;
  final String? code;
  final String? password;
  const AddSonScreen({
    super.key,
    this.password,
    this.code,
  });

  @override
  State<AddSonScreen> createState() => _AddSonScreenState();
}

class _AddSonScreenState extends State<AddSonScreen> {
  File? image;
  TextEditingController code = TextEditingController();
  TextEditingController password = TextEditingController();
  TextEditingController address = TextEditingController();
  PickupLocationLocalModels? pickupLocationLocalModels;
  // String? newAddress;
  LatLong? position;

  Future getImage() async {
    try {
      final images = await ImagePicker().pickImage(source: ImageSource.gallery);
      final imageFile = File(images!.path);
      setState(() {
        image = imageFile;
      });
    } on PlatformException catch (e, stackTrace) {
      debugPrint("catch error at Absence repo: $e");
      debugPrint(stackTrace.toString());
      print(e);
    }
  }

  @override
  void initState() {
    print("code working ${widget.code}");
    print("this is working ${widget.password}");
    if (widget.code != null && widget.password != null) {
      code = TextEditingController(text: widget.code);
      password = TextEditingController(text: widget.password);
    }
    super.initState();
  }

  Widget _buildFieldLabel(String label) {
    return Text(
      label,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: TColor.text,
      ),
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String hintText,
    required IconData icon,
    required TextInputType inputType,
    required Function(String) onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: TColor.fillFormFieldB,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: inputType,
        onChanged: onChanged,
        style: TextStyle(
          fontSize: 16.sp,
          color: TColor.text,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: TColor.tabColors,
            fontSize: 14.sp,
          ),
          prefixIcon: Container(
            margin: EdgeInsets.all(12.w),
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: TColor.mainColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Icon(
              icon,
              color: TColor.mainColor,
              size: 20.sp,
            ),
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 16.h,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        rightWidget: const SizedBox(),
        titleWidget: Row(
          children: [
            CustomText(
              text: AppStrings.addSon.tr(),
              fontSize: 18,
              textAlign: TextAlign.center,
              fontW: FontWeight.w600,
              color: TColor.white,
            ),
            const SizedBox(width: 10.0),
          ],
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.grey.withValues(alpha: 0.05),
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 30.h),

                // Header Section
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(24.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Container(
                        width: 80.w,
                        height: 80.w,
                        decoration: BoxDecoration(
                          color: TColor.mainColor.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.person_add_rounded,
                          size: 40.sp,
                          color: TColor.mainColor,
                        ),
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        AppStrings.addSon.tr(),
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: TColor.text,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'add_child_description'.tr(),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: TColor.tabColors,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 30.h),

                // Form Section
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(24.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Code Field
                      _buildFieldLabel(AppStrings.code.tr()),
                      SizedBox(height: 8.h),
                      _buildFormField(
                        controller: code,
                        hintText: AppStrings.code.tr(),
                        icon: Icons.qr_code_rounded,
                        inputType: TextInputType.number,
                        onChanged: (value) => code.text = value,
                      ),

                      SizedBox(height: 20.h),

                      // Password Field
                      _buildFieldLabel(AppStrings.password.tr()),
                      SizedBox(height: 8.h),
                      _buildFormField(
                        controller: password,
                        hintText: AppStrings.password.tr(),
                        icon: Icons.lock_outline_rounded,
                        inputType: TextInputType.number,
                        onChanged: (value) => password.text = value,
                      ),

                      SizedBox(height: 20.h),

                      // Address Field
                      _buildFieldLabel(AppStrings.address.tr()),
                      SizedBox(height: 8.h),
                      _buildFormField(
                        controller: address,
                        hintText: AppStrings.address.tr(),
                        icon: Icons.home_outlined,
                        inputType: TextInputType.text,
                        onChanged: (value) => address.text = value,
                      ),

                      SizedBox(height: 20.h),

                      // Location Field
                      _buildFieldLabel(AppStrings.getLocations.tr()),
                      SizedBox(height: 8.h),
                      _buildLocationField(),
                    ],
                  ),
                ),

                BlocConsumer<AddSonCubit, AddSonStates>(
                  listener: (context, states) {
                    if (states is AddSonSuccessStates) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          backgroundColor: TColor.greenSuccess,
                          content: CustomText(
                            text: states.addSonModels!.message,
                            fontSize: 18,
                            maxLine: 5,
                            color: TColor.white,
                          ),
                        ),
                      );
                      BlocProvider.of<ChildrenCubit>(context).getStudent();
                      Navigator.pop(context);
                    } else if (states is AddSonErrorStates) {
                      Logger().e(states.error);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          backgroundColor: TColor.redAccent,
                          content: CustomText(
                            text: states.error,
                            fontSize: 18,
                            maxLine: 5,
                            color: TColor.white,
                          ),
                        ),
                      );
                    }
                  },
                  builder: (context, states) {
                    if (states is! AddSonLoadingStates) {
                      return _buildSubmitButton();
                    } else {
                      return Container(
                        height: 60.h,
                        decoration: BoxDecoration(
                          color: TColor.mainColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(16.r),
                        ),
                        child: Center(
                          child: CircularProgressIndicator(
                            color: TColor.mainColor,
                          ),
                        ),
                      );
                    }
                  },
                ),

                SizedBox(height: 30.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLocationField() {
    return InkWell(
      onTap: () async {
        pickupLocationLocalModels = await Navigator.push(
          context,
          MaterialPageRoute(builder: (ctx) => const PickLocationWidget()),
        );
        setState(() {});
        if (pickupLocationLocalModels != null) {
          position = LatLong(
            pickupLocationLocalModels!.lat!,
            pickupLocationLocalModels!.long!,
          );
        }
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: TColor.fillFormFieldB,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: 12.w),
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: TColor.mainColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Icon(
                Icons.location_on_outlined,
                color: TColor.mainColor,
                size: 20.sp,
              ),
            ),
            Expanded(
              child: Text(
                pickupLocationLocalModels != null
                    ? pickupLocationLocalModels!.address ??
                        AppStrings.getLocations.tr()
                    : AppStrings.getLocations.tr(),
                style: TextStyle(
                  color: pickupLocationLocalModels != null
                      ? TColor.text
                      : TColor.tabColors,
                  fontSize: 14.sp,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: TColor.tabColors,
              size: 16.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 60.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            TColor.mainColor,
            TColor.mainColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: TColor.mainColor.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            if (position != null) {
              context.read<AddSonCubit>().addSon(
                    code: code.text,
                    password: password.text,
                    address: address.text,
                    latitude: position!.latitude.toString(),
                    longitude: position!.longitude.toString(),
                  );
            } else {
              context.read<AddSonCubit>().addSon(
                    code: code.text,
                    password: password.text,
                    address: address.text,
                  );
            }
          },
          borderRadius: BorderRadius.circular(16.r),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.person_add_rounded,
                  color: Colors.white,
                  size: 24.sp,
                ),
                SizedBox(width: 12.w),
                Text(
                  AppStrings.add.tr(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

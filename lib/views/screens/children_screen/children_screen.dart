import 'package:busaty_parents/helper/cache_helper.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:logger/logger.dart';
import 'package:busaty_parents/bloc/cubit/children_cubit/children_cubit.dart';
import 'package:busaty_parents/bloc/cubit/son_by_id_cubit/son_by_id_cubit.dart';
import 'package:busaty_parents/views/screens/add_son_screen/add_son_screen.dart';
import 'package:busaty_parents/views/screens/son_data_screen/son_data_screen.dart';
import 'package:busaty_parents/widgets/custom_appbar.dart';

import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../constant/path_route_name.dart';
import '../../../services/ad_mob_service.dart';
import '../../../services/payment_service.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../utils/helpers.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';
import '../show_son_on_map_screen/show_son_on_map_screen.dart';

class ChildrenScreen extends StatefulWidget {
  static const routeName = PathRouteName.childrenScreen;

  const ChildrenScreen({super.key});

  @override
  State<ChildrenScreen> createState() => _ChildrenScreenState();
}

class _ChildrenScreenState extends State<ChildrenScreen> {
  // Ad Related
  late AdMobService _adMobService;
  BannerAd? _banner;
  BannerAd? _banner1;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (PaymentService.instance.isProUser == false &&
        subscriptionStatus == false) {
      _adMobService = GetIt.instance.get<AdMobService>();
      _adMobService.initialization.then((value) {
        setState(() {
          _banner = BannerAd(
            adUnitId: _adMobService.bannerAdUnitId!,
            size: AdSize.fullBanner,
            request: const AdRequest(),
            listener: _adMobService.bannerListener,
          )..load();
          _banner1 = BannerAd(
            adUnitId: _adMobService.bannerAdUnitId!,
            size: AdSize.fullBanner,
            request: const AdRequest(),
            listener: _adMobService.bannerListener,
          )..load();
        });
      });
    } else {
      setState(() {
        _banner = null;
        _banner1 = null;
      });
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    BlocProvider.of<ChildrenCubit>(context).getStudent();
  }

  Widget _buildImprovedTable(ChildrenSuccessState state) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 20.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  TColor.mainColor,
                  TColor.mainColor.withValues(alpha: 0.8)
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'image'.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Text(
                    AppStrings.name.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: Text(
                    AppStrings.school.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    AppStrings.show.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          // Data rows
          if (state.childrens!.isEmpty)
            Container(
              padding: EdgeInsets.all(32.w),
              child: Column(
                children: [
                  Icon(
                    Icons.inbox_outlined,
                    size: 48.sp,
                    color: TColor.tabColors,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    AppStrings.notFound.tr(),
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: TColor.tabColors,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          else
            ...state.childrens!.asMap().entries.map((entry) {
              final index = entry.key;
              final child = entry.value;
              final isEven = index % 2 == 0;

              return _buildTableRow(
                child: child,
                isEven: isEven,
                isLast: index == state.childrens!.length - 1,
              );
            }).toList(),
        ],
      ),
    );
  }

  Widget _buildTableRow({
    required dynamic child,
    required bool isEven,
    required bool isLast,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 20.w),
      decoration: BoxDecoration(
        color: isEven ? Colors.grey.withValues(alpha: 0.05) : Colors.white,
        border: Border(
          bottom: isLast
              ? BorderSide.none
              : BorderSide(
                  color: Colors.grey.withValues(alpha: 0.15), width: 1),
        ),
        borderRadius: isLast
            ? BorderRadius.only(
                bottomLeft: Radius.circular(16.r),
                bottomRight: Radius.circular(16.r),
              )
            : null,
      ),
      child: Row(
        children: [
          // Student Image
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Center(
                child: Container(
                  width: 40.w,
                  height: 40.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: TColor.mainColor.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: ClipOval(
                    child: _buildStudentImage(child.logo),
                  ),
                ),
              ),
            ),
          ),
          // Student Name
          Expanded(
            flex: 4,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              child: Text(
                child.name ?? "--",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: TColor.text,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          // School
          Expanded(
            flex: 4,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Text(
                child.schools?.name ?? "--",
                style: TextStyle(
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w500,
                  color: TColor.text,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          // Actions
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTapDown: (details) {
                _showOptionsMenu(context, details.globalPosition, child);
              },
              child: Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: TColor.mainColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.more_vert,
                  color: TColor.mainColor,
                  size: 20.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStudentImage(String? logoUrl) {
    // التحقق من صحة الرابط
    if (logoUrl == null ||
        logoUrl.isEmpty ||
        logoUrl == "null" ||
        logoUrl.startsWith("file://") ||
        logoUrl == "default.png" ||
        !logoUrl.startsWith('http')) {
      return _buildDefaultAvatar();
    }

    // التحقق من صحة URI
    final uri = Uri.tryParse(logoUrl);
    if (uri == null || !uri.hasAbsolutePath) {
      return _buildDefaultAvatar();
    }

    return Image.network(
      logoUrl,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        return _buildDefaultAvatar();
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Container(
          color: TColor.mainColor.withValues(alpha: 0.1),
          child: Center(
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: TColor.mainColor,
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
            ),
          ),
        );
      },
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      color: TColor.mainColor.withValues(alpha: 0.1),
      child: Icon(
        Icons.person,
        color: TColor.mainColor,
        size: 20.sp,
      ),
    );
  }

  void _showOptionsMenu(BuildContext context, Offset position, dynamic child) {
    Helpers.customShowDialog(
      context,
      position: position,
      onTapShow: () {
        context.read<SonByIdCubit>().getSonById(
              studentId: child.id.toString(),
            );
        Navigator.of(context)
          ..pop()
          ..push(MaterialPageRoute(builder: (ctx) {
            return SonDataScreen(son: child);
          }));
      },
      onTapMorningTrip: () {
        Logger().e(child);
        Navigator.of(context)
          ..pop()
          ..push(MaterialPageRoute(builder: (ctx) {
            return ShowSonOnTheMapScreen(
              busId: child.busId.toString(),
              logoPath: child.logoPath,
              studentId: child.id!,
              sourceLat: double.parse(child.latitude ?? '0.0'),
              sourceLong: double.parse(child.longitude ?? '0.0'),
              destinationLat: double.parse(child.schools!.latitude!),
              destinationLong: double.parse(child.schools!.longitude!),
              tripType: 'start_day',
            );
          }));
      },
      onTapEveningTrip: () {
        Navigator.of(context)
          ..pop()
          ..push(MaterialPageRoute(builder: (ctx) {
            return ShowSonOnTheMapScreen(
              busId: child.busId.toString(),
              logoPath: child.logoPath,
              studentId: child.id!,
              sourceLat: double.parse(child.latitude ?? '0.0'),
              sourceLong: double.parse(child.longitude ?? '0.0'),
              destinationLat: double.parse(child.schools!.latitude!),
              destinationLong: double.parse(child.schools!.longitude!),
              tripType: 'end_day',
            );
          }));
      },
      onTapDelete: () {
        Navigator.pop(context);
        BlocProvider.of<ChildrenCubit>(context).deleteStudent(id: child.id!);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.children.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    TColor.mainColor,
                    TColor.mainColor.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: TColor.mainColor.withValues(alpha: 0.3),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    Navigator.push(context, MaterialPageRoute(builder: (ctx) {
                      return const AddSonScreen();
                    }));
                  },
                  borderRadius: BorderRadius.circular(16.r),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      vertical: 15.h,
                      horizontal: 20.w,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: EdgeInsets.all(6.w),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Icon(
                            Icons.add_rounded,
                            color: Colors.white,
                            size: 20.sp,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Text(
                          'add_child'.tr(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            10.verticalSpace,
            _banner1 == null
                ? const SizedBox()
                : SizedBox(
                    height: 60,
                    width: 1.sw,
                    child: AdWidget(ad: _banner1!),
                  ),
            BlocBuilder<ChildrenCubit, ChildrenState>(
              builder: (context, state) {
                if (state is ChildrenInitialState) {
                  BlocProvider.of<ChildrenCubit>(context).getStudent();
                  return const Center(
                      child: CircularProgressIndicator(
                    color: TColor.mainColor,
                  ));
                }
                if (state is ChildrenSuccessState) {
                  return _buildImprovedTable(state);
                } else {
                  return Center(
                    child: SizedBox(
                      child: CustomText(
                        text: AppStrings.notFound.tr(),
                        fontSize: 18,
                        textAlign: TextAlign.center,
                        fontW: FontWeight.w600,
                        color: TColor.text,
                      ),
                    ),
                  );
                }
              },
            ),
            10.verticalSpace,
            _banner == null
                ? const SizedBox()
                : SizedBox(
                    height: 60,
                    // width: 1.sw,
                    child: AdWidget(ad: _banner!),
                  ),
          ],
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:busaty_parents/bloc/cubit/absence_cubit/absence_cubit.dart';
import 'package:busaty_parents/bloc/cubit/absence_cubit/absence_state.dart';
import 'package:busaty_parents/constant/path_route_name.dart';

import '../../../config/global_variable.dart';
import '../../../config/theme_colors.dart';
import '../../../services/ad_mob_service.dart';
import '../../../services/payment_service.dart';
import '../../../translations/local_keys.g.dart';
import '../../../utils/assets_utils.dart';
import '../../../utils/helpers.dart';
import '../../../widgets/custom_appbar.dart';
import '../../custom_widgets/build_table_row_widget.dart';
import '../../custom_widgets/custom_text.dart';

class AbsenteeismRequestsScreen extends StatefulWidget {
  static const String routeName = PathRouteName.absenteeismRequestsScreen;
  const AbsenteeismRequestsScreen({super.key});

  @override
  State<AbsenteeismRequestsScreen> createState() =>
      _AbsenteeismRequestsScreenState();
}

class _AbsenteeismRequestsScreenState extends State<AbsenteeismRequestsScreen> {
  // Ad Related
  late AdMobService _adMobService;
  BannerAd? _banner;
  BannerAd? _banner1;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (PaymentService.instance.isProUser == false &&
        subscriptionStatus == false) {
      _adMobService = GetIt.instance.get<AdMobService>();
      _adMobService.initialization.then((value) {
        setState(() {
          _banner = BannerAd(
            adUnitId: _adMobService.bannerAdUnitId!,
            size: AdSize.fullBanner,
            request: const AdRequest(),
            listener: _adMobService.bannerListener,
          )..load();
          _banner1 = BannerAd(
            adUnitId: _adMobService.bannerAdUnitId!,
            size: AdSize.fullBanner,
            request: const AdRequest(),
            listener: _adMobService.bannerListener,
          )..load();
        });
      });
    } else {
      setState(() {
        _banner = null;
        _banner1 = null;
      });
    }
  }

  Widget _buildImprovedTable(AbsenceSucssesState state) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  TColor.mainColor,
                  TColor.mainColor.withValues(alpha: 0.8)
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    AppStrings.name.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    AppStrings.date.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    AppStrings.type.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    AppStrings.show.tr(),
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          // Data rows
          if (state.Absences!.isEmpty)
            Container(
              padding: EdgeInsets.all(32.w),
              child: Column(
                children: [
                  Icon(
                    Icons.inbox_outlined,
                    size: 48.sp,
                    color: TColor.tabColors,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    AppStrings.notFound.tr(),
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: TColor.tabColors,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          else
            ...state.Absences!.asMap().entries.map((entry) {
              final index = entry.key;
              final absence = entry.value;
              final isEven = index % 2 == 0;

              return _buildTableRow(
                absence: absence,
                isEven: isEven,
                isLast: index == state.Absences!.length - 1,
              );
            }).toList(),
        ],
      ),
    );
  }

  Widget _buildTableRow({
    required dynamic absence,
    required bool isEven,
    required bool isLast,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
      decoration: BoxDecoration(
        color: isEven ? Colors.grey.withValues(alpha: 0.05) : Colors.white,
        border: Border(
          bottom: isLast
              ? BorderSide.none
              : BorderSide(
                  color: Colors.grey.withValues(alpha: 0.2), width: 0.5),
        ),
        borderRadius: isLast
            ? BorderRadius.only(
                bottomLeft: Radius.circular(16.r),
                bottomRight: Radius.circular(16.r),
              )
            : null,
      ),
      child: Row(
        children: [
          // Student Name
          Expanded(
            flex: 3,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w),
              child: Text(
                absence.studentName ?? "--",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: TColor.text,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          // Date
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Column(
                children: [
                  Text(
                    _formatDate(absence.attendence_date ?? ""),
                    style: TextStyle(
                      fontSize: 13.sp,
                      fontWeight: FontWeight.w500,
                      color: TColor.text,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          // Type
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 6.h, horizontal: 8.w),
                decoration: BoxDecoration(
                  color:
                      _getTypeColor(absence.attendence_type).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(
                    color: _getTypeColor(absence.attendence_type),
                    width: 1,
                  ),
                ),
                child: Text(
                  _getTypeText(absence.attendence_type),
                  style: TextStyle(
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w600,
                    color: _getTypeColor(absence.attendence_type),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
          // Delete Action
          Expanded(
            flex: 1,
            child: GestureDetector(
              onTap: () {
                _showDeleteConfirmation(context, absence);
              },
              child: Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: Colors.red.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.delete_outline,
                  color: Colors.red,
                  size: 20.sp,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String date) {
    if (date.isEmpty) return "--";
    try {
      final DateTime parsedDate = DateTime.parse(date);
      return DateFormat('dd/MM/yyyy').format(parsedDate);
    } catch (e) {
      return date;
    }
  }

  String _getTypeText(String? type) {
    switch (type) {
      case "start_day":
        return AppStrings.morningTrip.tr();
      case "end_day":
        return AppStrings.eveningTrip.tr();
      default:
        return AppStrings.fullDay.tr();
    }
  }

  Color _getTypeColor(String? type) {
    switch (type) {
      case "start_day":
        return Colors.orange;
      case "end_day":
        return Colors.blue;
      default:
        return TColor.mainColor;
    }
  }

  void _showDeleteConfirmation(BuildContext context, dynamic absence) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          title: Text(
            'تأكيد الحذف',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'هل أنت متأكد من حذف طلب الغياب؟',
            style: TextStyle(fontSize: 16.sp),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'cancel'.tr(),
                style: TextStyle(
                  color: TColor.tabColors,
                  fontSize: 14.sp,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                BlocProvider.of<AbsenceCubit>(context).cancelAbsence(
                  absenceId: absence.id,
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                'delete'.tr(),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        titleWidget: CustomText(
          text: AppStrings.absenteeismRequests.tr(),
          fontSize: 18,
          textAlign: TextAlign.center,
          fontW: FontWeight.w600,
          color: TColor.white,
        ),
        leftWidget: context.locale.toString() == "ar"
            ? InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(AppAssets.arrowBack),
              )
            : InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: SvgPicture.asset(
                  AppAssets.forwardArrow,
                  colorFilter:
                      const ColorFilter.mode(TColor.white, BlendMode.srcIn),
                  width: 25.w,
                  height: 25.w,
                ),
              ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: InkWell(
                onTap: () {
                  Navigator.pushNamed(context, PathRouteName.addRequestAbsence);
                },
                child: Icon(
                  Icons.add_circle_outline,
                  size: 30.sp,
                  color: TColor.mainColor,
                ),
              ),
            ),
            10.verticalSpace,
            _banner1 == null
                ? const SizedBox()
                : SizedBox(
                    height: 60,
                    width: 1.sw,
                    child: AdWidget(ad: _banner1!),
                  ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 8.0),
                clipBehavior: Clip.antiAlias,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15.0),
                ),
                child: BlocBuilder<AbsenceCubit, AbsenceState>(
                  builder: (context, state) {
                    if (state is AbsenceInitialState) {
                      return const Center(
                          child: CircularProgressIndicator(
                        color: TColor.mainColor,
                      ));
                    }
                    if (state is AbsenceSucssesState) {
                      return _buildImprovedTable(state);
                    } else {
                      return Center(
                        child: SizedBox(
                          child: CustomText(
                            text: AppStrings.notFound.tr(),
                            fontSize: 18,
                            textAlign: TextAlign.center,
                            fontW: FontWeight.w600,
                            color: TColor.text,
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
            ),
            _banner == null
                ? const SizedBox()
                : SizedBox(
                    height: 60,
                    // width: 1.sw,
                    child: AdWidget(ad: _banner!),
                  ),
            10.verticalSpace,
          ],
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../config/theme_colors.dart';
import '../translations/local_keys.g.dart';
import '../widgets/student_widgets/custom_container_dialog_w.dart';

class Helpers {
  static customShowDialog(
    BuildContext context, {
    Function()? onTapMorningTrip,
    Function()? onTapShow,
    Function()? onTapEveningTrip,
    Function()? onTapDelete,
    required Offset position,
    List<PopupMenuItem> items = const [],
  }) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    Offset offset = overlay.localToGlobal(Offset.zero);

    final dx =
        context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
    final dy = position.dy + 15;

    showMenu(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(16.sp)),
      ),
      color: Colors.white,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      context: context,
      position: RelativeRect.fromLTRB(
        dx,
        dy,
        0,
        0,
      ),
      items: [
        _buildImprovedMenuItem(
          icon: Icons.wb_sunny_outlined,
          title: AppStrings.morningTrip.tr(),
          color: Colors.orange,
          onTap: onTapMorningTrip,
        ),
        _buildImprovedMenuItem(
          icon: Icons.nights_stay_outlined,
          title: AppStrings.eveningTrip.tr(),
          color: Colors.indigo,
          onTap: onTapEveningTrip,
        ),
        _buildImprovedMenuItem(
          icon: Icons.visibility_outlined,
          title: AppStrings.show.tr(),
          color: TColor.mainColor,
          onTap: onTapShow,
        ),
        ...items,
        _buildImprovedMenuItem(
          icon: Icons.delete_outline,
          title: AppStrings.delete.tr(),
          color: Colors.red,
          onTap: onTapDelete,
        ),
      ],
    );
  }

  static PopupMenuItem _buildImprovedMenuItem({
    required IconData icon,
    required String title,
    required Color color,
    Function()? onTap,
  }) {
    return PopupMenuItem(
      padding: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.sp),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: Row(
            children: [
              Container(
                width: 36.w,
                height: 36.w,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: TColor.text,
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 12.sp,
                color: Colors.grey.withValues(alpha: 0.5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // static customClassroomsShowDialog(
  //   BuildContext context, {
  //   Function()? onTapEdit,
  //   Function()? onTapDelete,
  //   required Offset position,
  //   List<PopupMenuItem> items = const [],
  // }) {
  //   final RenderBox overlay =
  //       Overlay.of(context).context.findRenderObject() as RenderBox;
  //   Offset offset = overlay.localToGlobal(Offset.zero);
  //
  //   final dx = context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
  //   final dy = position.dy + 15;
  //
  //   showMenu(
  //     context: context,
  //     position: RelativeRect.fromLTRB(
  //       dx,
  //       dy,
  //       0,
  //       0,
  //     ),
  //     items: [
  //       PopupMenuItem(
  //         child: Center(
  //           child: CustomContainerDialogW(
  //             icons: CupertinoIcons.create,
  //             name: LocaleKeys.edit.tr(),
  //             onTap: onTapEdit,
  //           ),
  //         ),
  //       ),
  //       PopupMenuItem(
  //         child: Center(
  //           child: CustomContainerDialogW(
  //             icons: CupertinoIcons.delete,
  //             name: LocaleKeys.delete.tr(),
  //             onTap: onTapDelete,
  //           ),
  //         ),
  //       ),
  //       ...items,
  //     ],
  //   );
  // }

  static customAbsenceShowDialog(
    BuildContext context, {
    Function()? onTapShow,
    required Offset position,
    List<PopupMenuItem> items = const [],
  }) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    Offset offset = overlay.localToGlobal(Offset.zero);

    final dx =
        context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
    final dy = position.dy + 15;

    showMenu(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(15.sp))),
      context: context,
      position: RelativeRect.fromLTRB(
        dx,
        dy,
        0,
        0,
      ),
      items: [
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: Icons.remove_red_eye,
              name: AppStrings.show.tr(),
              onTap: onTapShow,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  static customAddressRequestShowDialog(
    BuildContext context, {
    Function()? onTapDelete,
    required Offset position,
    List<PopupMenuItem> items = const [],
  }) {
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    Offset offset = overlay.localToGlobal(Offset.zero);

    final dx =
        context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
    final dy = position.dy + 15;

    showMenu(
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(15.sp))),
      context: context,
      position: RelativeRect.fromLTRB(
        dx,
        dy,
        0,
        0,
      ),
      items: [
        PopupMenuItem(
          child: Center(
            child: CustomContainerDialogW(
              icons: CupertinoIcons.delete,
              name: AppStrings.delete.tr(),
              onTap: onTapDelete,
            ),
          ),
        ),
        ...items,
      ],
    );
  }

  // static customBusShowDialog(
  //   BuildContext context, {
  //   Function()? onTapShow,
  //   Function()? onTapEdit,
  //   Function()? onTapDelete,
  //   Function()? onTapAddToBus,
  //   required Offset position,
  //   List<PopupMenuItem> items = const [],
  // }) {
  //   final RenderBox overlay =
  //       Overlay.of(context).context.findRenderObject() as RenderBox;
  //   Offset offset = overlay.localToGlobal(Offset.zero);
  //
  //   final dx = context.locale.toString() == "ar" ? offset.dx - 100 : offset.dx + 100;
  //   final dy = position.dy + 15;
  //
  //   showMenu(
  //     context: context,
  //     position: RelativeRect.fromLTRB(
  //       dx,
  //       dy,
  //       0,
  //       0,
  //     ),
  //     items: [
  //       PopupMenuItem(
  //         child: Center(
  //           child: CustomContainerDialogW(
  //             icons: Icons.remove_red_eye,
  //             name: LocaleKeys.show.tr(),
  //             onTap: onTapShow,
  //           ),
  //         ),
  //       ),
  //       ...items,
  //       PopupMenuItem(
  //         child: Center(
  //           child: CustomContainerDialogW(
  //             icons: CupertinoIcons.create,
  //             name: LocaleKeys.edit.tr(),
  //             onTap: onTapEdit,
  //           ),
  //         ),
  //       ),
  //       PopupMenuItem(
  //         child: Center(
  //           child: CustomContainerDialogW(
  //             icons: CupertinoIcons.delete,
  //             name: LocaleKeys.delete.tr(),
  //             onTap: onTapDelete,
  //           ),
  //         ),
  //       ),
  //       PopupMenuItem(
  //         child: Center(
  //           child: CustomContainerDialogW(
  //             icons: CupertinoIcons.add,
  //             name: LocaleKeys.addStudentToBus.tr(),
  //             onTap: onTapAddToBus,
  //           ),
  //         ),
  //       ),
  //     ],
  //   );
  // }
}

import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

class MaintenanceScreen extends StatelessWidget {
  final String message;
  final String appName;
  final VoidCallback? onRetry;

  const MaintenanceScreen({
    super.key,
    required this.message,
    required this.appName,
    this.onRetry,
  });

  // Helper method for localization
  String _getLocalizedText(
      BuildContext context, String arabic, String english) {
    return context.locale.toString() == "ar" ? arabic : english;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Icon/Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(60),
                ),
                child: Icon(
                  Icons.build_rounded,
                  size: 60,
                  color: Colors.blue.shade600,
                ),
              ),

              const SizedBox(height: 32),

              // Title
              Text(
                _getLocalizedText(context, "تحت الصيانة", "Under Maintenance"),
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // App Name
              if (appName.isNotEmpty)
                Text(
                  appName,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.blue.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),

              const SizedBox(height: 24),

              // Message
              Text(
                message.isNotEmpty
                    ? message
                    : _getLocalizedText(
                        context,
                        "التطبيق قيد التحديث حالياً. يرجى المحاولة مرة أخرى لاحقاً.",
                        "The app is currently being updated. Please try again later."),
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black54,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 40),

              // Loading indicator
              const CircularProgressIndicator(
                strokeWidth: 3,
              ),

              const SizedBox(height: 16),

              Text(
                _getLocalizedText(context, "جاري التحقق...", "Checking..."),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black45,
                ),
              ),

              const SizedBox(height: 40),

              // Retry button (if callback provided)
              if (onRetry != null)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: onRetry,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: Text(
                      _getLocalizedText(context, "إعادة المحاولة", "Retry"),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 24),

              // Footer text
              Text(
                _getLocalizedText(
                    context, "شكراً لصبركم", "Thank you for your patience"),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black38,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

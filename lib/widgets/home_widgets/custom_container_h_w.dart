import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';

class CustomContainerHW extends StatelessWidget {
  final Widget? icon;
  final String? title;
  final Function()? onTap;
  const CustomContainerHW({
    super.key,
    this.icon,
    this.title,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20.r),
      child: Container(
        width: 172.w,
        height: 100.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              TColor.backgroundContainer,
              TColor.backgroundContainer.withValues(alpha: 0.8),
            ],
          ),
          boxShadow: [
            BoxShadow(
              color: TColor.backgroundContainer.withValues(alpha: 0.3),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon Container with background
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.15),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Center(
                child: SizedBox(
                  width: 32.w,
                  height: 32.w,
                  child: icon!,
                ),
              ),
            ),
            SizedBox(height: 8.h),
            CustomText(
              text: title!.tr(),
              fontW: FontWeight.w500,
              fontSize: 14,
              color: TColor.white,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';

class CustomContainerHW extends StatelessWidget {
  final Widget? icon;
  final String? title;
  final Function()? onTap;
  const CustomContainerHW({
    super.key,
    this.icon,
    this.title,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Define colors for each service
    List<List<Color>> serviceColors = [
      [const Color(0xFF4CAF50), const Color(0xFF66BB6A)], // Green for Children
      [const Color(0xFFFF9800), const Color(0xFFFFB74D)], // Orange for Absence
      [const Color(0xFF2196F3), const Color(0xFF42A5F5)], // Blue for Settings
      [
        const Color(0xFF9C27B0),
        const Color(0xFFBA68C8)
      ], // Purple for Address Change
    ];

    // Define icons for each service
    List<IconData> serviceIcons = [
      Icons.groups_rounded, // Children
      Icons.event_busy_rounded, // Absence
      Icons.settings_rounded, // Settings
      Icons.location_on_rounded, // Address Change
    ];

    // Get the index based on title to determine color and icon
    int serviceIndex = 0;
    if (title == "children")
      serviceIndex = 0;
    else if (title == "absenteeismRequests")
      serviceIndex = 1;
    else if (title == "setting")
      serviceIndex = 2;
    else if (title == "requestChangeAddress") serviceIndex = 3;

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20.r),
      child: Container(
        width: 160.w,
        height: 90.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.r),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: serviceColors[serviceIndex],
          ),
          boxShadow: [
            BoxShadow(
              color: serviceColors[serviceIndex][0].withValues(alpha: 0.3),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Stack(
          children: [
            // Background pattern
            Positioned(
              top: -10,
              right: -10,
              child: Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withValues(alpha: 0.1),
                ),
              ),
            ),

            // Main content
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon Container with background
                  Container(
                    width: 45.w,
                    height: 45.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withValues(alpha: 0.2),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1.5,
                      ),
                    ),
                    child: Icon(
                      serviceIcons[serviceIndex],
                      color: Colors.white,
                      size: 24.sp,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  CustomText(
                    text: title!.tr(),
                    fontW: FontWeight.w600,
                    fontSize: 13,
                    color: Colors.white,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

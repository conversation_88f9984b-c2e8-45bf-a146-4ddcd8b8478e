import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';

class CustomContainerHW extends StatelessWidget {
  final Widget? icon;
  final String? title;
  final Function()? onTap;
  const CustomContainerHW({
    super.key,
    this.icon,
    this.title,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Define unique colors for each service - completely different colors
    List<Color> serviceColors = [
      const Color(0xFFE91E63), // Pink for Children
      const Color(0xFFFF9800), // Orange for Absence
      const Color(0xFF673AB7), // Deep Purple for Settings
      const Color(0xFF009688), // Teal for Address Change
    ];

    // Define unique background colors (lighter versions)
    List<Color> backgroundColors = [
      const Color(0xFFFCE4EC), // Light Pink
      const Color(0xFFFFF3E0), // Light Orange
      const Color(0xFFEDE7F6), // Light Deep Purple
      const Color(0xFFE0F2F1), // Light Teal
    ];

    // Define unique and appropriate icons for each service
    List<IconData> serviceIcons = [
      Icons.child_care_rounded, // Children - Child care icon
      Icons.event_busy_rounded, // Absence - Calendar with X
      Icons.settings_applications_rounded, // Settings - Settings gear
      Icons.location_city_rounded, // Address Change - City/Location icon
    ];

    // Get the index based on title to determine color and icon
    int serviceIndex = 0;
    if (title == "children") {
      serviceIndex = 0;
    } else if (title == "absenteeismRequests") {
      serviceIndex = 1;
    } else if (title == "setting") {
      serviceIndex = 2;
    } else if (title == "requestChangeAddress") {
      serviceIndex = 3;
    }

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16.r),
      child: Container(
        width: 160.w,
        height: 90.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: backgroundColors[serviceIndex],
          border: Border.all(
            color: serviceColors[serviceIndex].withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: serviceColors[serviceIndex].withValues(alpha: 0.15),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon Container
            Container(
              width: 50.w,
              height: 50.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: serviceColors[serviceIndex],
                boxShadow: [
                  BoxShadow(
                    color: serviceColors[serviceIndex].withValues(alpha: 0.3),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                serviceIcons[serviceIndex],
                color: Colors.white,
                size: 26.sp,
              ),
            ),
            SizedBox(height: 8.h),
            // Service Title
            CustomText(
              text: title!.tr(),
              fontW: FontWeight.w600,
              fontSize: 12,
              color: serviceColors[serviceIndex],
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

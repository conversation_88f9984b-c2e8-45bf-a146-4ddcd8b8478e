import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';

class CustomContainerHW extends StatelessWidget {
  final String? title;
  final Function()? onTap;
  const CustomContainerHW({
    super.key,
    this.title,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Define unique colors for each service - completely different colors
    List<Color> serviceColors = [
      const Color(0xFF6A1B9A), // Purple for Children
      const Color(0xFFE53935), // Red for Absence
      const Color(0xFF00ACC1), // Cyan for Settings
      const Color(0xFFFF8F00), // Orange for Address Change
    ];

    // Define unique background colors (lighter versions)
    List<Color> backgroundColors = [
      const Color(0xFFF3E5F5), // Light Purple
      const Color(0xFFFFEBEE), // Light Red
      const Color(0xFFE0F7FA), // Light Cyan
      const Color(0xFFFFF3E0), // Light Orange
    ];

    // Define unique and appropriate icons for each service
    List<IconData> serviceIcons = [
      Icons.family_restroom_rounded, // Children - Family icon
      Icons.sick_rounded, // Absence - Sick icon
      Icons.build_circle_rounded, // Settings - Build circle
      Icons.map_rounded, // Address Change - Map icon
    ];

    // Get the index based on title to determine color and icon
    int serviceIndex = 0;

    if (title == "sons") {
      serviceIndex = 0;
    } else if (title == "absence_requests") {
      serviceIndex = 1;
    } else if (title == "settings") {
      serviceIndex = 2;
    } else if (title == "requestChangeAddress") {
      serviceIndex = 3;
    }

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16.r),
      child: Container(
        width: 160.w,
        height: 90.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: backgroundColors[serviceIndex],
          border: Border.all(
            color: serviceColors[serviceIndex].withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: serviceColors[serviceIndex].withValues(alpha: 0.15),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon Container
            Container(
              width: 50.w,
              height: 50.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: serviceColors[serviceIndex],
                boxShadow: [
                  BoxShadow(
                    color: serviceColors[serviceIndex].withValues(alpha: 0.3),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                serviceIcons[serviceIndex],
                color: Colors.white,
                size: 26.sp,
              ),
            ),
            SizedBox(height: 8.h),
            // Service Title
            CustomText(
              text: title!.tr(),
              fontW: FontWeight.w600,
              fontSize: 12,
              color: serviceColors[serviceIndex],
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/utils/sized_box.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';

class CustomContainerHW extends StatelessWidget {
  final Widget? icon;
  final String? title;
  final Function()? onTap;
  const CustomContainerHW({
    super.key,
    this.icon,
    this.title,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Define unique colors for each service - completely different colors
    List<Color> serviceColors = [
      const Color(0xFF1976D2), // Blue for Children
      const Color(0xFFD32F2F), // Red for Absence
      const Color(0xFF388E3C), // Green for Settings
      const Color(0xFFF57C00), // Amber for Address Change
    ];

    // Define unique background colors (lighter versions)
    List<Color> backgroundColors = [
      const Color(0xFFE3F2FD), // Light Blue
      const Color(0xFFFFEBEE), // Light Red
      const Color(0xFFE8F5E8), // Light Green
      const Color(0xFFFFF3E0), // Light Amber
    ];

    // Define unique and appropriate icons for each service
    List<IconData> serviceIcons = [
      Icons.people_rounded, // Children - People icon
      Icons.cancel_presentation_rounded, // Absence - Cancel presentation
      Icons.admin_panel_settings_rounded, // Settings - Admin panel
      Icons.edit_location_alt_rounded, // Address Change - Edit location
    ];

    // Get the index based on title to determine color and icon
    int serviceIndex = 0;
    if (title == "children") {
      serviceIndex = 0;
    } else if (title == "absenteeismRequests") {
      serviceIndex = 1;
    } else if (title == "setting") {
      serviceIndex = 2;
    } else if (title == "requestChangeAddress") {
      serviceIndex = 3;
    }

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16.r),
      child: Container(
        width: 160.w,
        height: 90.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          color: backgroundColors[serviceIndex],
          border: Border.all(
            color: serviceColors[serviceIndex].withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: serviceColors[serviceIndex].withValues(alpha: 0.15),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon Container
            Container(
              width: 50.w,
              height: 50.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: serviceColors[serviceIndex],
                boxShadow: [
                  BoxShadow(
                    color: serviceColors[serviceIndex].withValues(alpha: 0.3),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                serviceIcons[serviceIndex],
                color: Colors.white,
                size: 26.sp,
              ),
            ),
            SizedBox(height: 8.h),
            // Service Title
            CustomText(
              text: title!.tr(),
              fontW: FontWeight.w600,
              fontSize: 12,
              color: serviceColors[serviceIndex],
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

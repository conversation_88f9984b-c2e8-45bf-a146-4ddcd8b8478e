import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:busaty_parents/notification_service/utils/logger.dart';
import 'package:busaty_parents/bloc/cubit/notifications_cubit/notifications_cubit.dart';
import 'package:busaty_parents/config/global_variable.dart';
import 'package:busaty_parents/config/theme_colors.dart';
import 'package:busaty_parents/views/custom_widgets/custom_text.dart';

import '../../translations/local_keys.g.dart';
import '../../views/screens/notifications_screen/notifications_screen.dart';

class CustomNameW extends StatelessWidget {
  const CustomNameW({super.key});

  @override
  Widget build(BuildContext context) {
    print(context.locale.toString());
    int hour = DateTime.now().hour;
    return Container(
      padding:
          EdgeInsets.only(top: 15.w, right: 20.w, left: 20.w, bottom: 15.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // User Info Section
          Expanded(
            child: Row(
              children: [
                // User Avatar
                Container(
                  width: 50.w,
                  height: 50.w,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.2),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.person_rounded,
                    color: Colors.white,
                    size: 28.sp,
                  ),
                ),
                SizedBox(width: 12.w),

                // User Text Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomText(
                        text: hour >= 5 && hour < 12
                            ? AppStrings.goodMorning.tr()
                            : AppStrings.goodEvening.tr(),
                        color: Colors.white.withValues(alpha: 0.9),
                        fontW: FontWeight.w500,
                        fontSize: 14,
                      ),
                      SizedBox(height: 2.h),
                      CustomText(
                        text: userName ?? "ولي الأمر",
                        color: TColor.white,
                        fontW: FontWeight.w600,
                        fontSize: 18,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Notification Button
          Container(
            width: 45.w,
            height: 45.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.15),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: InkWell(
              onTap: () {
                Logger.w("working ==================");
                NotificationsCubit.get(context)
                    .getAllNotifications(page: 1, isFirst: true);
                Navigator.pushNamed(context, NotificationsScreen.routeName);
              },
              borderRadius: BorderRadius.circular(22.5.w),
              child: Icon(
                Icons.notifications_rounded,
                color: TColor.white,
                size: 22.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

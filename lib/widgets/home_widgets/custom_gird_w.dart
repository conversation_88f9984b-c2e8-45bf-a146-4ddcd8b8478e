import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get_it/get_it.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:busaty_parents/constant/path_route_name.dart';
import 'package:busaty_parents/utils/assets_utils.dart';
import 'package:busaty_parents/widgets/home_widgets/custom_container_h_w.dart';

import '../../bloc/cubit/absence_cubit/absence_cubit.dart';
import '../../bloc/cubit/address_change_cubit/address_change_cubit.dart';
import '../../config/global_variable.dart';
import '../../services/ad_mob_service.dart';
import '../../services/payment_service.dart';
import '../../translations/local_keys.g.dart';

class CustomGirdW extends StatefulWidget {
  const CustomGirdW({super.key});

  @override
  State<CustomGirdW> createState() => _CustomGirdWState();
}

class _CustomGirdWState extends State<CustomGirdW> {
  // Ad Related
  late AdMobService _adMobService;
  InterstitialAd? _interstitial;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (PaymentService.instance.isProUser == false &&
        subscriptionStatus == false) {
      _adMobService = GetIt.instance.get<AdMobService>();
      _adMobService.initialization.then((value) {
        setState(() {
          _createInterstitialAd();
        });
      });
    } else {
      setState(() {
        _interstitial = null;
      });
    }
  }

  void _createInterstitialAd() {
    InterstitialAd.load(
        adUnitId: _adMobService.interstitialAdUnitId!,
        request: const AdRequest(),
        adLoadCallback:
            InterstitialAdLoadCallback(onAdLoaded: (InterstitialAd ad) {
          _interstitial = ad;
        }, onAdFailedToLoad: (LoadAdError error) {
          _interstitial = null;
        }));
  }

  void _showInterstitialAd() {
    if (_interstitial != null) {
      _interstitial!.fullScreenContentCallback = FullScreenContentCallback(
          onAdDismissedFullScreenContent: (InterstitialAd ad) {
        ad.dispose();
        _createInterstitialAd();
      }, onAdFailedToShowFullScreenContent: (InterstitialAd ad, AdError error) {
        ad.dispose();
        _createInterstitialAd();
      });
      _interstitial!.show();
      _interstitial = null;
    }
  }

  List homeIcon = [
    assetsImages("group1.png"),
    assetsImages("group1.png"),
    assetsImages("setting.png"),
    assetsImages("location.png"),
  ];

  List homeTitle = [
    AppStrings.sons,
    AppStrings.absenceRequests,
    AppStrings.settings,
    AppStrings.requestChangeAddress,
  ];

  @override
  Widget build(BuildContext context) {
    print(context.locale.toString());
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: GridView.count(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        mainAxisSpacing: 16.w,
        crossAxisSpacing: 16.w,
        childAspectRatio: 1.6,
        children: List.generate(4, (index) {
          return CustomContainerHW(
            onTap: () async {
              if (index == 0) {
                // AudioSoundRangTone.instance.disposed();
                _showInterstitialAd();
                Navigator.pushNamed(context, PathRouteName.childrenScreen);
              } else if (index == 1) {
                _showInterstitialAd();
                Navigator.pushNamed(
                    context, PathRouteName.absenteeismRequestsScreen);
                AbsenceCubit.get(context).getAbsences();
              } else if (index == 2) {
                _showInterstitialAd();
                Navigator.pushNamed(context, PathRouteName.setting);
              } else if (index == 3) {
                _showInterstitialAd();
                Navigator.pushNamed(
                    context, PathRouteName.requestChangeAddress);
                AddressChangeCubit.get(context).getAddressChanges();
              }
            },
            icon: Image.asset(
              homeIcon[index],
              width: 32.w,
              height: 32.w,
              color: Colors.white,
            ),
            title: homeTitle[index],
          );
        }),
      ),
    );
  }
}
